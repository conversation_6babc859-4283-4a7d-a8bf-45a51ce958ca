"""
Jira Site Selection Routes

API endpoints for handling Jira multi-site selection during OAuth flow.
"""

import logging
from typing import Dict, List, Optional

from fastapi import APIRouter, HTTPException, Query, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.services.authentication_service import get_auth_service_client

logger = logging.getLogger(__name__)

jira_site_router = APIRouter(prefix="/oauth/jira", tags=["jira-site-selection"])


class SiteInfo(BaseModel):
    """Information about an Atlassian site."""
    id: str
    name: str
    url: str
    avatar_url: Optional[str] = None


class AccessibleSitesResponse(BaseModel):
    """Response for accessible sites endpoint."""
    success: bool
    message: str
    sites: List[SiteInfo]
    selection_token: str


class SiteSelectionRequest(BaseModel):
    """Request for site selection."""
    selection_token: str
    selected_cloud_id: str


class SiteSelectionResponse(BaseModel):
    """Response for site selection."""
    success: bool
    message: str
    redirect_url: Optional[str] = None
    selected_site: Optional[SiteInfo] = None


@jira_site_router.get(
    "/accessible-sites",
    response_model=AccessibleSitesResponse,
    summary="Get Accessible Atlassian Sites",
    description="""
    Retrieve accessible Atlassian sites for a user during OAuth flow.
    
    This endpoint is called when a user has multiple Atlassian sites and needs to select one.
    The selection_token returned should be used in the site selection endpoint.
    """,
)
async def get_accessible_sites(
    token: str = Query(..., description="Site selection token from OAuth callback")
) -> AccessibleSitesResponse:
    """Get accessible Atlassian sites for site selection."""
    try:
        # Get the authentication service client
        auth_service = get_auth_service_client()
        
        # Call the authentication service to get accessible sites
        result = await auth_service.get_accessible_sites(selection_token=token)
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("message", "Failed to retrieve accessible sites")
            )
        
        # Transform sites data
        sites = [
            SiteInfo(
                id=site["id"],
                name=site["name"],
                url=site["url"],
                avatar_url=site.get("avatar_url")
            )
            for site in result.get("sites", [])
        ]
        
        return AccessibleSitesResponse(
            success=True,
            message=f"Found {len(sites)} accessible sites",
            sites=sites,
            selection_token=token
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving accessible sites: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving sites"
        )


@jira_site_router.post(
    "/select-site",
    response_model=SiteSelectionResponse,
    summary="Select Atlassian Site",
    description="""
    Process user's site selection and complete OAuth flow.
    
    This endpoint completes the OAuth flow by storing credentials for the selected site.
    After successful selection, the user will be redirected to the original redirect URL.
    """,
)
async def select_site(request: SiteSelectionRequest) -> SiteSelectionResponse:
    """Process site selection and complete OAuth flow."""
    try:
        # Get the authentication service client
        auth_service = get_auth_service_client()
        
        # Call the authentication service to complete site selection
        result = await auth_service.complete_site_selection(
            selection_token=request.selection_token,
            selected_cloud_id=request.selected_cloud_id
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("message", "Failed to complete site selection")
            )
        
        # Transform selected site data if available
        selected_site = None
        if result.get("selected_site"):
            site_data = result["selected_site"]
            selected_site = SiteInfo(
                id=site_data["id"],
                name=site_data["name"],
                url=site_data["url"],
                avatar_url=site_data.get("avatar_url")
            )
        
        return SiteSelectionResponse(
            success=True,
            message=result.get("message", "Site selected successfully"),
            redirect_url=result.get("redirect_url"),
            selected_site=selected_site
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing site selection: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while completing site selection"
        )


@jira_site_router.get(
    "/site-selection",
    summary="Site Selection Page",
    description="""
    Redirect endpoint for site selection UI.
    
    This endpoint can be used to serve a site selection page or redirect to a frontend application
    that handles the site selection UI.
    """,
)
async def site_selection_page(
    token: str = Query(..., description="Site selection token")
):
    """Serve or redirect to site selection page."""
    try:
        # For now, return a JSON response with the token
        # In a real implementation, this might redirect to a frontend page
        return JSONResponse(
            content={
                "message": "Site selection required",
                "selection_token": token,
                "instructions": "Use the /oauth/jira/accessible-sites endpoint to get available sites, then /oauth/jira/select-site to complete selection"
            }
        )
        
    except Exception as e:
        logger.error(f"Error serving site selection page: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
