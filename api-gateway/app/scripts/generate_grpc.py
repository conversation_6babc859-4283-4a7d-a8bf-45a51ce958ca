import os
import subprocess
from pathlib import Path
import shutil
from dotenv import load_dotenv

load_dotenv()


def clone_repository():
    """Clone the repository if it doesn't exist, using PAT if required."""
    repo_url = os.getenv("REPO_URL")
    git_token = os.getenv("GIT_TOKEN")
    branch = os.getenv("ENV")
    branch = "org-refactor"

    if not repo_url:
        raise ValueError("Environment variable REPO_URL is not set.")

    if git_token:
        repo_url = repo_url.replace("https://", f"https://oauth2:{git_token}@")

    repo_name = repo_url.split("/")[-1].replace(".git", "")
    project_root = Path.cwd() / repo_name

    if not project_root.exists():
        try:
            subprocess.run(["git", "clone", "--branch", branch, repo_url], check=True)
            print(f"Successfully cloned repository: {repo_url} {branch}")
        except subprocess.CalledProcessError as e:
            print(f"Error cloning repository: {e}")
            raise
    # else:
    #     print(f"Repository already exists at: {project_root}, removing and cloning fresh copy")
    #     # Remove existing directory

    #     shutil.rmtree(project_root)
    #     # Clone repository again
    #     try:
    #         subprocess.run(["git", "clone", "--branch", branch, repo_url], check=True)
    #         print(f"Successfully cloned fresh copy of repository: {repo_url} {branch}")
    #     except subprocess.CalledProcessError as e:
    #         print(f"Error cloning repository: {e}")
    #         raise

    return project_root


def generate_grpc_code():
    """Generate gRPC code from proto files."""
    # Get the root directory of the project
    current_dir = Path(__file__).resolve().parent
    project_root = clone_repository()
    proto_dir = project_root
    output_dir = project_root.parent / "app" / "grpc_"

    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    # Create __init__.py in the grpc directory
    (output_dir / "__init__.py").touch()

    # List of proto files to compile
    proto_files = [
        "user.proto",
        "admin.proto",
        "notification.proto",
        "communication.proto",
        "workflow.proto",
        "agent.proto",
        "mcp.proto",
        "organisation.proto",
        "authentication.proto",
        "agent_graph.proto",
        "search.proto",
        "connector.proto",
        "analytics.proto",
        "provider.proto",
        "payment.proto",
        # Add other proto files here as they are created
    ]

    for proto_file in proto_files:
        # Command to generate gRPC code
        command = [
            "python3",
            "-m",
            "grpc_tools.protoc",
            f"--proto_path={proto_dir}",
            f"--python_out={output_dir}",
            f"--grpc_python_out={output_dir}",
            str(proto_dir / proto_file),
        ]

        try:
            subprocess.run(command, check=True)
            print(f"Successfully generated gRPC code for {proto_file}")

            # Fix imports in generated files
            base_name = proto_file.replace(".proto", "")
            for file in output_dir.glob(f"{base_name}*pb2*.py"):
                content = file.read_text()
                # Replace relative imports with absolute imports
                content = content.replace(
                    f"import {base_name}_pb2", f"from app.grpc_ import {base_name}_pb2"
                )
                file.write_text(content)

            print(f"Successfully fixed imports in generated files for {proto_file}")

        except subprocess.CalledProcessError as e:
            print(f"Error generating gRPC code for {proto_file}: {e}")
            raise


if __name__ == "__main__":
    generate_grpc_code()
