"""add short token to invites

Revision ID: 20250521_001
Revises: 20250515_002
Create Date: 2025-05-21 15:24:58.000000

"""
from alembic import op
import sqlalchemy as sa
from enum import Enum
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = '20250521_001'
down_revision = '20250515_002'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add the short_token column to the invites table
    op.add_column('invites', sa.Column('short_token', sa.String(12), nullable=True, unique=True))
    
    # Create an index for faster lookups by short token
    op.create_index('idx_invites_short_token', 'invites', ['short_token'])


def downgrade() -> None:
    # Drop the index first
    op.drop_index('idx_invites_short_token')
    
    # Then drop the column
    op.drop_column('invites', 'short_token')