"""
Async service for tracking Google Drive sync job status and progress.
"""
import json
import logging
import time
from typing import Optional, List, Dict, Any
from enum import Enum
from dataclasses import dataclass, asdict
from app.utils.async_redis import async_redis_service

logger = logging.getLogger(__name__)

class SyncJobStatus(Enum):
    """Sync job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

@dataclass
class SyncJobProgress:
    """Sync job progress tracking."""
    job_id: str
    user_id: str
    organisation_id: str
    job_type: str
    status: SyncJobStatus
    
    # Progress tracking
    progress_percentage: float = 0.0
    current_step: str = ""
    total_items: int = 0
    processed_items: int = 0
    
    # Timing
    created_at: float = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    estimated_completion: Optional[float] = None
    
    # Results
    files_synced: int = 0
    folders_synced: int = 0
    errors: List[Dict[str, Any]] = None
    
    # Worker info
    worker_id: Optional[str] = None
    queue_name: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()
        if self.errors is None:
            self.errors = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SyncJobProgress':
        """Create instance from dictionary."""
        data['status'] = SyncJobStatus(data['status'])
        return cls(**data)

class AsyncSyncStatusService:
    """
    Async service for tracking Google Drive sync job status and progress.
    """
    
    def __init__(self):
        """Initialize the status service."""
        self.redis = async_redis_service
        self.status_ttl = 86400 * 7  # Keep status for 7 days
    
    async def create_job_status(self, job_progress: SyncJobProgress) -> bool:
        """
        Create initial job status.
        
        Args:
            job_progress: Job progress object
            
        Returns:
            True if created successfully
        """
        try:
            key = f"sync_status:{job_progress.job_id}"
            data = json.dumps(job_progress.to_dict())
            
            success = await self.redis.set(key, data, ex=self.status_ttl)
            
            if success:
                # Add to user's job list
                user_jobs_key = f"user_sync_jobs:{job_progress.user_id}"
                await self.redis.zadd(
                    user_jobs_key, 
                    {job_progress.job_id: job_progress.created_at}
                )
                await self.redis.expire(user_jobs_key, self.status_ttl)
                
                logger.info(f"Created job status for {job_progress.job_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error creating job status: {e}")
            return False
    
    async def update_job_status(
        self, 
        job_id: str, 
        status: Optional[SyncJobStatus] = None,
        progress_percentage: Optional[float] = None,
        current_step: Optional[str] = None,
        processed_items: Optional[int] = None,
        total_items: Optional[int] = None,
        worker_id: Optional[str] = None,
        queue_name: Optional[str] = None,
        error: Optional[str] = None,
        files_synced: Optional[int] = None,
        folders_synced: Optional[int] = None
    ) -> bool:
        """
        Update job status with new information.
        
        Args:
            job_id: Job ID
            status: New status
            progress_percentage: Progress percentage (0-100)
            current_step: Current step description
            processed_items: Number of processed items
            total_items: Total number of items
            worker_id: Worker ID processing the job
            queue_name: Queue name
            error: Error message
            files_synced: Number of files synced
            folders_synced: Number of folders synced
            
        Returns:
            True if updated successfully
        """
        try:
            job_progress = await self.get_job_status(job_id)
            if not job_progress:
                logger.warning(f"Job {job_id} not found for status update")
                return False
            
            # Update fields
            if status:
                job_progress.status = status
                if status == SyncJobStatus.RUNNING and not job_progress.started_at:
                    job_progress.started_at = time.time()
                elif status in [SyncJobStatus.COMPLETED, SyncJobStatus.FAILED, SyncJobStatus.CANCELLED]:
                    job_progress.completed_at = time.time()
            
            if progress_percentage is not None:
                job_progress.progress_percentage = min(100.0, max(0.0, progress_percentage))
                
                # Estimate completion time
                if job_progress.started_at and progress_percentage > 0:
                    elapsed = time.time() - job_progress.started_at
                    estimated_total = elapsed / (progress_percentage / 100.0)
                    job_progress.estimated_completion = job_progress.started_at + estimated_total
            
            if current_step:
                job_progress.current_step = current_step
            
            if processed_items is not None:
                job_progress.processed_items = processed_items
            
            if total_items is not None:
                job_progress.total_items = total_items
            
            if worker_id:
                job_progress.worker_id = worker_id
            
            if queue_name:
                job_progress.queue_name = queue_name
            
            if files_synced is not None:
                job_progress.files_synced = files_synced
            
            if folders_synced is not None:
                job_progress.folders_synced = folders_synced
            
            if error:
                job_progress.errors.append({
                    'timestamp': time.time(),
                    'error': error
                })
            
            # Save updated status
            key = f"sync_status:{job_id}"
            data = json.dumps(job_progress.to_dict())
            success = await self.redis.set(key, data, ex=self.status_ttl)
            
            if success:
                logger.debug(f"Updated job status for {job_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating job status: {e}")
            return False
    
    async def get_job_status(self, job_id: str) -> Optional[SyncJobProgress]:
        """
        Get current job status.
        
        Args:
            job_id: Job ID
            
        Returns:
            Job progress object or None if not found
        """
        try:
            key = f"sync_status:{job_id}"
            data = await self.redis.get(key)
            
            if not data:
                return None
            
            job_data = json.loads(data)
            return SyncJobProgress.from_dict(job_data)
            
        except Exception as e:
            logger.error(f"Error getting job status: {e}")
            return None
    
    async def get_user_jobs(self, user_id: str, limit: int = 50) -> List[SyncJobProgress]:
        """
        Get all jobs for a user.
        
        Args:
            user_id: User ID
            limit: Maximum number of jobs to return
            
        Returns:
            List of job progress objects
        """
        try:
            user_jobs_key = f"user_sync_jobs:{user_id}"
            
            # Get job IDs sorted by creation time (newest first)
            job_ids = await self.redis.zrangebyscore(
                user_jobs_key, '-inf', '+inf', start=0, num=limit
            )
            job_ids.reverse()  # Newest first
            
            jobs = []
            for job_id in job_ids:
                job_status = await self.get_job_status(job_id)
                if job_status:
                    jobs.append(job_status)
            
            return jobs
            
        except Exception as e:
            logger.error(f"Error getting user jobs: {e}")
            return []
    
    async def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a running job.
        
        Args:
            job_id: Job ID
            
        Returns:
            True if cancelled successfully
        """
        try:
            job_progress = await self.get_job_status(job_id)
            if not job_progress:
                return False
            
            if job_progress.status in [SyncJobStatus.PENDING, SyncJobStatus.RUNNING, SyncJobStatus.PAUSED]:
                # Update status to cancelled
                success = await self.update_job_status(job_id, status=SyncJobStatus.CANCELLED)
                
                if success:
                    logger.info(f"Cancelled job {job_id}")
                
                return success
            
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling job: {e}")
            return False
    
    async def cleanup_old_statuses(self, days: int = 7) -> int:
        """
        Clean up old job statuses.
        
        Args:
            days: Number of days to keep statuses
            
        Returns:
            Number of statuses cleaned up
        """
        try:
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            cleaned_count = 0
            
            # This is a simplified cleanup - in production, you'd want to scan keys more efficiently
            # For now, we'll rely on Redis TTL to handle cleanup
            
            logger.info(f"Cleanup completed, {cleaned_count} old statuses removed")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning up old statuses: {e}")
            return 0
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """
        Get queue statistics.
        
        Returns:
            Dictionary with queue statistics
        """
        try:
            stats = {
                'total_jobs': 0,
                'pending_jobs': 0,
                'running_jobs': 0,
                'completed_jobs': 0,
                'failed_jobs': 0
            }
            
            # This would require scanning all job statuses
            # For now, return basic stats
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting queue stats: {e}")
            return {}

# Global async status service instance
async_status_service = AsyncSyncStatusService()
