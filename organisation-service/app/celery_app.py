"""
Async Celery application configuration for Google Drive sync.
"""
from celery import Celery
from kombu import Queue, Exchange
import os
import asyncio
from app.core.config import settings

# Create Celery instance with async support
celery_app = Celery('gdrive_async_sync')

# Configure Celery for async operations
celery_app.conf.update(
    # Broker and backend
    broker_url=os.getenv('RABBITMQ_URL', 'pyamqp://guest:guest@localhost:5672//'),
    result_backend=os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
    
    # Serialization
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Worker configuration for async
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=False,
    
    # Task routing with priority queues
    task_routes={
        'gdrive.async_sync_drive': {'queue': 'heavy_sync'},
        'gdrive.async_sync_folder': {'queue': 'medium_sync'},
        'gdrive.async_sync_file_by_url': {'queue': 'light_sync'},
        'gdrive.async_sync_priority': {'queue': 'priority_sync'},
    },
    
    # Define queues with different priorities
    task_queues=(
        Queue('priority_sync', 
              Exchange('priority', type='direct'), 
              routing_key='priority_sync',
              queue_arguments={'x-max-priority': 10}),
        Queue('heavy_sync', 
              Exchange('heavy', type='direct'), 
              routing_key='heavy_sync',
              queue_arguments={'x-max-priority': 5}),
        Queue('medium_sync', 
              Exchange('medium', type='direct'), 
              routing_key='medium_sync',
              queue_arguments={'x-max-priority': 3}),
        Queue('light_sync', 
              Exchange('light', type='direct'), 
              routing_key='light_sync',
              queue_arguments={'x-max-priority': 1}),
    ),
    
    # Retry configuration
    task_default_retry_delay=60,
    task_max_retries=3,
    
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Result expiration
    result_expires=3600,  # 1 hour
    
    # Task time limits
    task_time_limit=3600,  # 1 hour
    task_soft_time_limit=3300,  # 55 minutes
)

# Auto-discover tasks
celery_app.autodiscover_tasks(['app.tasks'])

# Health check task
@celery_app.task(name='health_check')
def health_check():
    """Health check task for monitoring."""
    import time
    return {'status': 'healthy', 'timestamp': time.time()}

# Configure async event loop for Celery workers
def setup_async_loop():
    """Setup async event loop for Celery workers."""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop

# Worker initialization
@celery_app.task(bind=True)
def init_worker(self):
    """Initialize worker with async support."""
    setup_async_loop()
    return "Worker initialized with async support"
