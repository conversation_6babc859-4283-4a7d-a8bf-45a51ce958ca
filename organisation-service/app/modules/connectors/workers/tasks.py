from app.modules.connectors.handlers.gdrive.workers.sync_worker import (
    GoogleDriveSyncWorker,
)
from app.modules.connectors.workers.celery_worker import celery_app


@celery_app.task(name="sync_drive_task")
def sync_drive_task(organisation_id: str, full_sync: bool = False):
    worker = GoogleDriveSyncWorker()
    return worker.process_job_now(
        user_id=None, organisation_id=organisation_id, full_sync=full_sync
    )


@celery_app.task(name="sync_folders_by_ids_task")
def sync_folders_by_ids_task(organisation_id: str, folder_ids: list):
    worker = GoogleDriveSyncWorker()
    return worker._process_folder_sync_job(organisation_id, folder_ids)
