"""
Async Redis client wrapper for job status and caching.
"""
import redis.asyncio as redis
import json
import logging
from typing import Dict, Any, Optional, List, Union
import time
from app.core.config import settings

logger = logging.getLogger(__name__)

class AsyncRedisService:
    """
    Async Redis service for job status tracking and caching.
    """
    
    def __init__(self):
        """Initialize async Redis client."""
        self.redis_client = None
        self.connection_pool = None
        self._setup_connection()
    
    def _setup_connection(self):
        """Setup Redis connection pool."""
        try:
            redis_url = getattr(settings, 'REDIS_URL', 'redis://localhost:6379/0')
            self.connection_pool = redis.ConnectionPool.from_url(
                redis_url,
                max_connections=20,
                retry_on_timeout=True,
                decode_responses=True
            )
            self.redis_client = redis.Redis(connection_pool=self.connection_pool)
            logger.info("Async Redis connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to setup Redis connection: {e}")
            raise
    
    async def ping(self) -> bool:
        """Test Redis connection."""
        try:
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis ping failed: {e}")
            return False
    
    async def set(
        self, 
        key: str, 
        value: Union[str, Dict, List], 
        ex: Optional[int] = None
    ) -> bool:
        """
        Set a key-value pair in Redis.
        
        Args:
            key: Redis key
            value: Value to store (will be JSON serialized if not string)
            ex: Expiration time in seconds
            
        Returns:
            True if successful
        """
        try:
            if not isinstance(value, str):
                value = json.dumps(value)
            
            result = await self.redis_client.set(key, value, ex=ex)
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to set key {key}: {e}")
            return False
    
    async def get(self, key: str) -> Optional[str]:
        """
        Get value by key from Redis.
        
        Args:
            key: Redis key
            
        Returns:
            Value as string or None if not found
        """
        try:
            return await self.redis_client.get(key)
        except Exception as e:
            logger.error(f"Failed to get key {key}: {e}")
            return None
    
    async def get_json(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get JSON value by key from Redis.
        
        Args:
            key: Redis key
            
        Returns:
            Parsed JSON value or None if not found
        """
        try:
            value = await self.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"Failed to get JSON key {key}: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """
        Delete a key from Redis.
        
        Args:
            key: Redis key
            
        Returns:
            True if key was deleted
        """
        try:
            result = await self.redis_client.delete(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to delete key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if key exists in Redis.
        
        Args:
            key: Redis key
            
        Returns:
            True if key exists
        """
        try:
            result = await self.redis_client.exists(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to check existence of key {key}: {e}")
            return False
    
    async def zadd(
        self, 
        name: str, 
        mapping: Dict[str, float], 
        nx: bool = False
    ) -> int:
        """
        Add members to sorted set.
        
        Args:
            name: Sorted set name
            mapping: Dictionary of member -> score
            nx: Only add new members
            
        Returns:
            Number of members added
        """
        try:
            return await self.redis_client.zadd(name, mapping, nx=nx)
        except Exception as e:
            logger.error(f"Failed to zadd to {name}: {e}")
            return 0
    
    async def zrangebyscore(
        self, 
        name: str, 
        min_score: Union[str, float], 
        max_score: Union[str, float],
        start: int = 0,
        num: int = -1
    ) -> List[str]:
        """
        Get members from sorted set by score range.
        
        Args:
            name: Sorted set name
            min_score: Minimum score
            max_score: Maximum score
            start: Start offset
            num: Number of items to return
            
        Returns:
            List of members
        """
        try:
            return await self.redis_client.zrangebyscore(
                name, min_score, max_score, start=start, num=num
            )
        except Exception as e:
            logger.error(f"Failed to zrangebyscore from {name}: {e}")
            return []
    
    async def zrem(self, name: str, *values: str) -> int:
        """
        Remove members from sorted set.
        
        Args:
            name: Sorted set name
            values: Members to remove
            
        Returns:
            Number of members removed
        """
        try:
            return await self.redis_client.zrem(name, *values)
        except Exception as e:
            logger.error(f"Failed to zrem from {name}: {e}")
            return 0
    
    async def zcard(self, name: str) -> int:
        """
        Get sorted set cardinality.
        
        Args:
            name: Sorted set name
            
        Returns:
            Number of members in set
        """
        try:
            return await self.redis_client.zcard(name)
        except Exception as e:
            logger.error(f"Failed to get zcard of {name}: {e}")
            return 0
    
    async def hset(
        self, 
        name: str, 
        mapping: Dict[str, Union[str, int, float]]
    ) -> int:
        """
        Set hash fields.
        
        Args:
            name: Hash name
            mapping: Field -> value mapping
            
        Returns:
            Number of fields added
        """
        try:
            return await self.redis_client.hset(name, mapping=mapping)
        except Exception as e:
            logger.error(f"Failed to hset {name}: {e}")
            return 0
    
    async def hget(self, name: str, key: str) -> Optional[str]:
        """
        Get hash field value.
        
        Args:
            name: Hash name
            key: Field name
            
        Returns:
            Field value or None
        """
        try:
            return await self.redis_client.hget(name, key)
        except Exception as e:
            logger.error(f"Failed to hget {name}.{key}: {e}")
            return None
    
    async def hgetall(self, name: str) -> Dict[str, str]:
        """
        Get all hash fields and values.
        
        Args:
            name: Hash name
            
        Returns:
            Dictionary of field -> value
        """
        try:
            return await self.redis_client.hgetall(name)
        except Exception as e:
            logger.error(f"Failed to hgetall {name}: {e}")
            return {}
    
    async def expire(self, name: str, time: int) -> bool:
        """
        Set expiration time for key.
        
        Args:
            name: Key name
            time: Expiration time in seconds
            
        Returns:
            True if expiration was set
        """
        try:
            result = await self.redis_client.expire(name, time)
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to set expiration for {name}: {e}")
            return False
    
    async def close(self):
        """Close Redis connection."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            if self.connection_pool:
                await self.connection_pool.disconnect()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")

# Global async Redis service instance
async_redis_service = AsyncRedisService()
