"""
Async HTTP client wrapper for Google Drive API using httpx.
"""
import httpx
import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from google.oauth2 import service_account
from google.auth.transport.requests import Request
import time

logger = logging.getLogger(__name__)

class AsyncGoogleDriveClient:
    """
    Async HTTP client for Google Drive API operations.
    """
    
    def __init__(self, service_account_info: Dict[str, Any]):
        """
        Initialize async Google Drive client.
        
        Args:
            service_account_info: Service account credentials dictionary
        """
        self.service_account_info = service_account_info
        self.credentials = None
        self.client = None
        self.base_url = "https://www.googleapis.com/drive/v3"
        self._setup_credentials()
    
    def _setup_credentials(self):
        """Setup Google service account credentials."""
        try:
            self.credentials = service_account.Credentials.from_service_account_info(
                self.service_account_info,
                scopes=['https://www.googleapis.com/auth/drive.readonly']
            )
            logger.info("Service account credentials initialized")
        except Exception as e:
            logger.error(f"Failed to setup credentials: {e}")
            raise
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.client = httpx.AsyncClient(timeout=30.0)
        await self._refresh_token()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.client:
            await self.client.aclose()
    
    async def _refresh_token(self):
        """Refresh the access token if needed."""
        try:
            if not self.credentials.valid:
                # Use sync request for token refresh (Google Auth doesn't support async)
                request = Request()
                self.credentials.refresh(request)
            logger.debug("Access token refreshed")
        except Exception as e:
            logger.error(f"Failed to refresh token: {e}")
            raise
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Make async HTTP request to Google Drive API.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            params: Query parameters
            headers: Additional headers
            
        Returns:
            Response data as dictionary
        """
        await self._refresh_token()
        
        # Prepare headers
        request_headers = {
            'Authorization': f'Bearer {self.credentials.token}',
            'Content-Type': 'application/json'
        }
        if headers:
            request_headers.update(headers)
        
        url = f"{self.base_url}/{endpoint}"
        
        try:
            response = await self.client.request(
                method=method,
                url=url,
                params=params,
                headers=request_headers
            )
            response.raise_for_status()
            return response.json()
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error {e.response.status_code}: {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Request failed: {e}")
            raise
    
    async def list_files(
        self, 
        query: str = None,
        page_token: str = None,
        page_size: int = 100,
        fields: str = None
    ) -> Dict[str, Any]:
        """
        List files from Google Drive.
        
        Args:
            query: Search query
            page_token: Token for pagination
            page_size: Number of items per page
            fields: Fields to include in response
            
        Returns:
            Files list response
        """
        params = {
            'pageSize': page_size,
            'spaces': 'drive'
        }
        
        if query:
            params['q'] = query
        if page_token:
            params['pageToken'] = page_token
        if fields:
            params['fields'] = fields
        else:
            params['fields'] = (
                'nextPageToken, files(id, name, mimeType, parents, '
                'createdTime, modifiedTime, size, webViewLink, permissions)'
            )
        
        return await self._make_request('GET', 'files', params=params)
    
    async def get_file(self, file_id: str, fields: str = None) -> Dict[str, Any]:
        """
        Get file metadata by ID.
        
        Args:
            file_id: Google Drive file ID
            fields: Fields to include in response
            
        Returns:
            File metadata
        """
        params = {}
        if fields:
            params['fields'] = fields
        else:
            params['fields'] = (
                'id, name, mimeType, parents, createdTime, modifiedTime, '
                'size, webViewLink, permissions(emailAddress, role, type)'
            )
        
        return await self._make_request('GET', f'files/{file_id}', params=params)
    
    async def list_folder_contents(
        self, 
        folder_id: str,
        page_token: str = None,
        page_size: int = 100
    ) -> Dict[str, Any]:
        """
        List contents of a specific folder.
        
        Args:
            folder_id: Google Drive folder ID
            page_token: Token for pagination
            page_size: Number of items per page
            
        Returns:
            Folder contents response
        """
        query = f"'{folder_id}' in parents and trashed=false"
        return await self.list_files(
            query=query,
            page_token=page_token,
            page_size=page_size
        )
    
    async def get_file_permissions(self, file_id: str) -> List[Dict[str, Any]]:
        """
        Get file permissions.
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            List of permissions
        """
        try:
            response = await self._make_request('GET', f'files/{file_id}/permissions')
            return response.get('permissions', [])
        except Exception as e:
            logger.error(f"Failed to get permissions for file {file_id}: {e}")
            return []
    
    async def batch_get_files(self, file_ids: List[str]) -> List[Dict[str, Any]]:
        """
        Get multiple files in batch (simulated with concurrent requests).
        
        Args:
            file_ids: List of file IDs
            
        Returns:
            List of file metadata
        """
        tasks = [self.get_file(file_id) for file_id in file_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        files = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Failed to get file {file_ids[i]}: {result}")
            else:
                files.append(result)
        
        return files
    
    async def search_files(
        self, 
        query: str,
        max_results: int = 1000
    ) -> List[Dict[str, Any]]:
        """
        Search files with pagination handling.
        
        Args:
            query: Search query
            max_results: Maximum number of results
            
        Returns:
            List of matching files
        """
        all_files = []
        page_token = None
        
        while len(all_files) < max_results:
            page_size = min(100, max_results - len(all_files))
            
            response = await self.list_files(
                query=query,
                page_token=page_token,
                page_size=page_size
            )
            
            files = response.get('files', [])
            all_files.extend(files)
            
            page_token = response.get('nextPageToken')
            if not page_token:
                break
        
        return all_files[:max_results]

class AsyncRateLimiter:
    """
    Async rate limiter for API calls.
    """
    
    def __init__(self, requests_per_second: float = 10, burst_size: int = 20):
        """
        Initialize rate limiter.
        
        Args:
            requests_per_second: Maximum requests per second
            burst_size: Maximum burst size
        """
        self.requests_per_second = requests_per_second
        self.burst_size = burst_size
        self.tokens = burst_size
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> bool:
        """
        Acquire a token from the rate limiter.
        
        Returns:
            True if token acquired, False if rate limited
        """
        async with self._lock:
            now = time.time()
            
            # Refill tokens based on time elapsed
            time_elapsed = now - self.last_refill
            tokens_to_add = time_elapsed * self.requests_per_second
            self.tokens = min(self.tokens + tokens_to_add, self.burst_size)
            self.last_refill = now
            
            # Try to acquire token
            if self.tokens >= 1.0:
                self.tokens -= 1.0
                return True
            
            return False
    
    async def wait_for_token(self):
        """Wait until a token is available."""
        while not await self.acquire():
            await asyncio.sleep(0.1)
