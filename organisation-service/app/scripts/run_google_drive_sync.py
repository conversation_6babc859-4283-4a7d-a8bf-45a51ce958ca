#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run the Google Drive sync worker independently.
This can be used to manually trigger syncs or for debugging.
"""

import argparse
import os
import sys
import time
from pathlib import Path

import structlog

# Add the parent directory to sys.path to allow importing app modules
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent))

from app.modules.connectors.handlers.gdrive.services.neo4j_setup import (
    create_google_drive_constraints,
    create_google_drive_indexes,
)
from app.modules.connectors.handlers.gdrive.workers.sync_worker import (
    GoogleDriveSyncWorker,
)

logger = structlog.get_logger()


def parse_args():
    parser = argparse.ArgumentParser(description="Run Google Drive sync worker")
    parser.add_argument(
        "--user-id",
        help="User ID to sync (if not provided, will run as a worker for all users)",
    )
    parser.add_argument(
        "--full-sync",
        action="store_true",
        help="Perform a full sync instead of incremental",
    )
    parser.add_argument(
        "--worker-mode",
        action="store_true",
        help="Run in worker mode (continuous processing)",
    )
    parser.add_argument(
        "--poll-interval",
        type=int,
        default=5,
        help="Polling interval in seconds (for worker mode)",
    )
    return parser.parse_args()


def main():
    args = parse_args()

    # Create Neo4j constraints and indexes
    logger.info("Setting up Neo4j constraints and indexes")
    create_google_drive_constraints()
    create_google_drive_indexes()

    # Initialize worker
    worker = GoogleDriveSyncWorker(poll_interval=args.poll_interval)

    if args.worker_mode:
        # Run in worker mode (continuous processing)
        logger.info("Starting Google Drive sync worker")
        worker.start()

        try:
            # Keep the main thread alive
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Stopping Google Drive sync worker")
            worker.stop()
    else:
        # Run a single sync for a specific user
        if not args.user_id:
            logger.error("User ID is required when not running in worker mode")
            sys.exit(1)

        logger.info(
            f"Running {'full' if args.full_sync else 'incremental'} sync for user {args.user_id}"
        )
        success, message, files_synced, folders_synced = worker.process_job_now(
            args.user_id, args.full_sync
        )

        if success:
            logger.info(
                f"Sync completed successfully: {files_synced} files, {folders_synced} folders synced"
            )
        else:
            logger.error(f"Sync failed: {message}")
            sys.exit(1)


if __name__ == "__main__":
    main()
"""
Script to enqueue a Google Drive sync job using Celery.
"""

from app.modules.connectors.workers.tasks import (
    sync_drive_task,
    sync_folders_by_ids_task,
)

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Enqueue Google Drive sync job")
    parser.add_argument("--org", required=True, help="Organisation ID")
    parser.add_argument("--full", action="store_true", help="Full drive sync")
    parser.add_argument("--folders", nargs="*", help="List of folder IDs to sync")

    args = parser.parse_args()

    if args.folders:
        result = sync_folders_by_ids_task.delay(args.org, args.folders)
        print(f"Enqueued folder sync task: {result.id}")
    else:
        result = sync_drive_task.delay(args.org, full_sync=args.full)
        print(f"Enqueued drive sync task: {result.id}")
