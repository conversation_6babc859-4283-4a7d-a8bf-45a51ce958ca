"""
Async Celery tasks for Google Drive sync operations.
"""
import asyncio
import logging
import time
from typing import Dict, Any, List, Tuple
from celery import current_task
from celery.exceptions import Retry

from app.celery_app import celery_app
from app.utils.async_http_client import AsyncG<PERSON>gle<PERSON><PERSON><PERSON>lient, AsyncRateLimiter
from app.utils.async_redis import async_redis_service
from app.services.async_sync_status_service import (
    async_status_service, 
    SyncJobStatus, 
    SyncJobProgress
)
from app.modules.connectors.handlers.gdrive.services.google_drive_service import GoogleDriveService

logger = logging.getLogger(__name__)

# Global rate limiter for Google Drive API
rate_limiter = AsyncRateLimiter(requests_per_second=10, burst_size=20)

def run_async_task(async_func):
    """
    Decorator to run async functions in Celery tasks.
    """
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        return loop.run_until_complete(async_func(*args, **kwargs))
    
    return wrapper

@celery_app.task(
    bind=True,
    name='gdrive.async_sync_drive',
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    retry_backoff=True,
    retry_jitter=True,
    acks_late=True,
    time_limit=7200,  # 2 hours
    soft_time_limit=6600  # 1h 50m
)
@run_async_task
async def async_sync_drive(
    self, 
    organisation_id: str, 
    full_sync: bool = False,
    user_id: str = None
) -> Dict[str, Any]:
    """
    Async task to sync entire Google Drive for an organization.
    
    Args:
        organisation_id: Organization ID
        full_sync: Whether to perform full sync
        user_id: User ID (optional)
        
    Returns:
        Dict containing sync results
    """
    task_id = self.request.id
    worker_id = self.request.hostname
    
    try:
        # Initialize job status
        job_progress = SyncJobProgress(
            job_id=task_id,
            user_id=user_id or organisation_id,
            organisation_id=organisation_id,
            job_type='sync_drive',
            status=SyncJobStatus.RUNNING,
            worker_id=worker_id,
            queue_name='heavy_sync'
        )
        
        await async_status_service.create_job_status(job_progress)
        await async_status_service.update_job_status(
            task_id,
            current_step="Initializing Google Drive sync",
            progress_percentage=0.0
        )
        
        # Get service account credentials
        drive_service = GoogleDriveService()
        service_account_info = drive_service._get_service_account_info(organisation_id)
        
        if not service_account_info:
            await async_status_service.update_job_status(
                task_id,
                status=SyncJobStatus.FAILED,
                error="No service account credentials found"
            )
            raise Exception("No service account credentials found")
        
        # Update progress: Connecting to API
        await async_status_service.update_job_status(
            task_id,
            progress_percentage=10.0,
            current_step="Connecting to Google Drive API"
        )
        
        # Apply rate limiting
        await rate_limiter.wait_for_token()
        
        # Perform async sync
        async with AsyncGoogleDriveClient(service_account_info) as client:
            files_synced, folders_synced = await _async_sync_drive_contents(
                client, organisation_id, full_sync, task_id
            )
        
        # Update final success status
        await async_status_service.update_job_status(
            task_id,
            status=SyncJobStatus.COMPLETED,
            progress_percentage=100.0,
            current_step="Sync completed successfully",
            files_synced=files_synced,
            folders_synced=folders_synced,
            processed_items=files_synced + folders_synced
        )
        
        logger.info(f"Async sync completed for org {organisation_id}: {files_synced} files, {folders_synced} folders")
        
        return {
            'status': 'SUCCESS',
            'message': 'Google Drive sync completed successfully',
            'files_synced': files_synced,
            'folders_synced': folders_synced,
            'total_items': files_synced + folders_synced,
            'organisation_id': organisation_id
        }
        
    except Exception as exc:
        # Log the error
        logger.error(f"Async Google Drive sync failed for org {organisation_id}: {str(exc)}")
        
        # Update error status
        await async_status_service.update_job_status(
            task_id,
            status=SyncJobStatus.FAILED,
            error=str(exc)
        )
        
        # Retry logic with exponential backoff
        if self.request.retries < self.max_retries:
            retry_countdown = 60 * (2 ** self.request.retries)
            logger.info(f"Retrying async sync for org {organisation_id}, attempt {self.request.retries + 1} in {retry_countdown}s")
            
            await async_status_service.update_job_status(
                task_id,
                status=SyncJobStatus.PENDING,
                current_step=f"Retrying in {retry_countdown} seconds (attempt {self.request.retries + 1})"
            )
            
            raise self.retry(countdown=retry_countdown)
        
        raise exc

@celery_app.task(
    bind=True,
    name='gdrive.async_sync_folder',
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 30},
    acks_late=True,
    time_limit=1800,  # 30 minutes
    soft_time_limit=1500  # 25 minutes
)
@run_async_task
async def async_sync_folder_by_ids(
    self, 
    organisation_id: str, 
    folder_ids: List[str],
    user_id: str = None
) -> Dict[str, Any]:
    """
    Async task to sync specific folders by ID.
    
    Args:
        organisation_id: Organization ID
        folder_ids: List of folder IDs to sync
        user_id: User ID (optional)
        
    Returns:
        Dict containing sync results
    """
    task_id = self.request.id
    worker_id = self.request.hostname
    
    try:
        # Initialize status
        job_progress = SyncJobProgress(
            job_id=task_id,
            user_id=user_id or organisation_id,
            organisation_id=organisation_id,
            job_type='sync_folders',
            status=SyncJobStatus.RUNNING,
            worker_id=worker_id,
            queue_name='medium_sync',
            total_items=len(folder_ids)
        )
        
        await async_status_service.create_job_status(job_progress)
        await async_status_service.update_job_status(
            task_id,
            current_step=f"Starting sync of {len(folder_ids)} folders"
        )
        
        # Get service account credentials
        drive_service = GoogleDriveService()
        service_account_info = drive_service._get_service_account_info(organisation_id)
        
        if not service_account_info:
            await async_status_service.update_job_status(
                task_id,
                status=SyncJobStatus.FAILED,
                error="No service account credentials found"
            )
            raise Exception("No service account credentials found")
        
        # Process folders
        total_files = 0
        total_folders = 0
        synced_folders = []
        
        async with AsyncGoogleDriveClient(service_account_info) as client:
            for i, folder_id in enumerate(folder_ids):
                # Apply rate limiting
                await rate_limiter.wait_for_token()
                
                # Update progress
                progress = (i / len(folder_ids)) * 100
                step = f'Processing folder {i+1} of {len(folder_ids)}'
                
                await async_status_service.update_job_status(
                    task_id,
                    progress_percentage=progress,
                    current_step=step,
                    processed_items=i
                )
                
                # Sync individual folder
                try:
                    files, folders = await _async_sync_single_folder(
                        client, organisation_id, folder_id
                    )
                    total_files += files
                    total_folders += folders
                    synced_folders.append({
                        'folder_id': folder_id,
                        'files_synced': files,
                        'folders_synced': folders,
                        'status': 'success'
                    })
                    
                except Exception as folder_error:
                    logger.error(f"Error syncing folder {folder_id}: {folder_error}")
                    synced_folders.append({
                        'folder_id': folder_id,
                        'files_synced': 0,
                        'folders_synced': 0,
                        'status': 'failed',
                        'error': str(folder_error)
                    })
        
        # Final success update
        await async_status_service.update_job_status(
            task_id,
            status=SyncJobStatus.COMPLETED,
            progress_percentage=100.0,
            current_step="Folder sync completed",
            files_synced=total_files,
            folders_synced=total_folders,
            processed_items=len(folder_ids)
        )
        
        return {
            'status': 'SUCCESS',
            'folders_processed': len(folder_ids),
            'files_synced': total_files,
            'folders_synced': total_folders,
            'synced_folders': synced_folders,
            'organisation_id': organisation_id
        }
        
    except Exception as exc:
        logger.error(f"Async folder sync failed: {str(exc)}")
        await async_status_service.update_job_status(
            task_id,
            status=SyncJobStatus.FAILED,
            error=str(exc)
        )
        raise exc

@celery_app.task(
    bind=True,
    name='gdrive.async_sync_file_by_url',
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 30},
    acks_late=True,
    time_limit=600,  # 10 minutes
    soft_time_limit=480  # 8 minutes
)
@run_async_task
async def async_sync_file_by_url(
    self, 
    organisation_id: str, 
    drive_urls: List[str],
    agent_id: str = None,
    user_id: str = None
) -> Dict[str, Any]:
    """
    Async task to sync files by Google Drive URLs.
    
    Args:
        organisation_id: Organization ID
        drive_urls: List of Google Drive URLs
        agent_id: Agent ID (optional)
        user_id: User ID (optional)
        
    Returns:
        Dict containing sync results
    """
    task_id = self.request.id
    worker_id = self.request.hostname
    
    try:
        # Initialize status
        job_progress = SyncJobProgress(
            job_id=task_id,
            user_id=user_id or organisation_id,
            organisation_id=organisation_id,
            job_type='sync_files_by_url',
            status=SyncJobStatus.RUNNING,
            worker_id=worker_id,
            queue_name='light_sync',
            total_items=len(drive_urls)
        )
        
        await async_status_service.create_job_status(job_progress)
        await async_status_service.update_job_status(
            task_id,
            current_step=f"Starting sync of {len(drive_urls)} files"
        )
        
        # Get service account credentials
        drive_service = GoogleDriveService()
        service_account_info = drive_service._get_service_account_info(organisation_id)
        
        if not service_account_info:
            await async_status_service.update_job_status(
                task_id,
                status=SyncJobStatus.FAILED,
                error="No service account credentials found"
            )
            raise Exception("No service account credentials found")
        
        # Process files
        synced_files = []
        successful_syncs = 0
        failed_syncs = 0
        
        async with AsyncGoogleDriveClient(service_account_info) as client:
            for i, drive_url in enumerate(drive_urls):
                # Apply rate limiting
                await rate_limiter.wait_for_token()
                
                # Update progress
                progress = (i / len(drive_urls)) * 100
                step = f'Processing file {i+1} of {len(drive_urls)}'
                
                await async_status_service.update_job_status(
                    task_id,
                    progress_percentage=progress,
                    current_step=step,
                    processed_items=i
                )
                
                # Extract file ID from URL and sync
                try:
                    file_id = _extract_file_id_from_url(drive_url)
                    if file_id:
                        file_data = await client.get_file(file_id)
                        # Process file data (create in Neo4j, etc.)
                        # This would call the existing sync logic
                        
                        synced_files.append({
                            'file_id': file_id,
                            'file_name': file_data.get('name', 'Unknown'),
                            'drive_url': drive_url,
                            'sync_status': 'success',
                            'error_message': ''
                        })
                        successful_syncs += 1
                    else:
                        raise Exception("Invalid Google Drive URL")
                        
                except Exception as file_error:
                    logger.error(f"Error syncing file {drive_url}: {file_error}")
                    synced_files.append({
                        'file_id': '',
                        'file_name': '',
                        'drive_url': drive_url,
                        'sync_status': 'failed',
                        'error_message': str(file_error)
                    })
                    failed_syncs += 1
        
        # Final success update
        await async_status_service.update_job_status(
            task_id,
            status=SyncJobStatus.COMPLETED,
            progress_percentage=100.0,
            current_step="File sync completed",
            files_synced=successful_syncs,
            processed_items=len(drive_urls)
        )
        
        return {
            'status': 'SUCCESS',
            'message': f'Synced {successful_syncs} files, {failed_syncs} failed',
            'synced_files': synced_files,
            'total_files': len(drive_urls),
            'successful_syncs': successful_syncs,
            'failed_syncs': failed_syncs
        }
        
    except Exception as exc:
        logger.error(f"Async file sync failed: {str(exc)}")
        await async_status_service.update_job_status(
            task_id,
            status=SyncJobStatus.FAILED,
            error=str(exc)
        )
        raise exc

# Helper functions

async def _async_sync_drive_contents(
    client: AsyncGoogleDriveClient, 
    organisation_id: str, 
    full_sync: bool,
    task_id: str
) -> Tuple[int, int]:
    """
    Async sync of Google Drive contents.
    
    Args:
        client: Async Google Drive client
        organisation_id: Organization ID
        full_sync: Whether to perform full sync
        task_id: Task ID for progress updates
        
    Returns:
        Tuple of (files_synced, folders_synced)
    """
    files_synced = 0
    folders_synced = 0
    
    try:
        # Get all files and folders
        query = "trashed=false"
        all_items = await client.search_files(query, max_results=10000)
        
        total_items = len(all_items)
        await async_status_service.update_job_status(
            task_id,
            total_items=total_items,
            current_step=f"Found {total_items} items to sync"
        )
        
        # Process items in batches
        batch_size = 50
        for i in range(0, len(all_items), batch_size):
            batch = all_items[i:i + batch_size]
            
            for j, item in enumerate(batch):
                # Apply rate limiting
                await rate_limiter.wait_for_token()
                
                is_folder = item.get('mimeType') == 'application/vnd.google-apps.folder'
                
                if is_folder:
                    # Process folder (would call existing Neo4j creation logic)
                    folders_synced += 1
                else:
                    # Process file (would call existing Neo4j creation logic)
                    files_synced += 1
                
                # Update progress
                current_item = i + j + 1
                progress = (current_item / total_items) * 90 + 10  # 10-100% range
                
                await async_status_service.update_job_status(
                    task_id,
                    progress_percentage=progress,
                    current_step=f"Processed {current_item}/{total_items} items",
                    processed_items=current_item,
                    files_synced=files_synced,
                    folders_synced=folders_synced
                )
        
        return files_synced, folders_synced
        
    except Exception as e:
        logger.error(f"Error in async sync drive contents: {e}")
        raise

async def _async_sync_single_folder(
    client: AsyncGoogleDriveClient, 
    organisation_id: str, 
    folder_id: str
) -> Tuple[int, int]:
    """
    Async sync of a single folder.
    
    Args:
        client: Async Google Drive client
        organisation_id: Organization ID
        folder_id: Folder ID to sync
        
    Returns:
        Tuple of (files_synced, folders_synced)
    """
    try:
        # Get folder contents
        contents = await client.list_folder_contents(folder_id)
        items = contents.get('files', [])
        
        files_synced = 0
        folders_synced = 0
        
        for item in items:
            is_folder = item.get('mimeType') == 'application/vnd.google-apps.folder'
            
            if is_folder:
                folders_synced += 1
            else:
                files_synced += 1
        
        return files_synced, folders_synced
        
    except Exception as e:
        logger.error(f"Error syncing folder {folder_id}: {e}")
        return 0, 0

def _extract_file_id_from_url(drive_url: str) -> str:
    """
    Extract file ID from Google Drive URL.
    
    Args:
        drive_url: Google Drive URL
        
    Returns:
        File ID or empty string if not found
    """
    import re
    
    # Pattern for Google Drive file URLs
    patterns = [
        r'/file/d/([a-zA-Z0-9-_]+)',
        r'id=([a-zA-Z0-9-_]+)',
        r'/folders/([a-zA-Z0-9-_]+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, drive_url)
        if match:
            return match.group(1)
    
    return ""
