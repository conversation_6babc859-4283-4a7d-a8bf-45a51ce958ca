# Multi-Site Selection API Design

## Overview
This document outlines the API design for handling multiple Atlassian sites during OAuth authentication.

## Flow Diagram
```
1. User initiates OAuth → Authorization URL
2. User authorizes → OAuth callback with code
3. Exchange code for tokens
4. Check accessible resources (sites)
5a. Single site → Store credentials directly
5b. Multiple sites → Redirect to site selection
6. User selects site → Store credentials with cloud_id
7. Complete OAuth flow
```

## API Endpoints

### 1. Get Accessible Sites
**Endpoint**: `GET /oauth/jira/accessible-sites`
**Purpose**: Retrieve available Atlassian sites for a user during OAuth flow

**Query Parameters**:
- `state`: OAuth state token (required)

**Response**:
```json
{
  "success": true,
  "sites": [
    {
      "id": "cloud-id-1",
      "name": "Company Jira",
      "url": "https://company.atlassian.net",
      "avatarUrl": "https://avatar-url"
    },
    {
      "id": "cloud-id-2", 
      "name": "Personal Jira",
      "url": "https://personal.atlassian.net",
      "avatarUrl": "https://avatar-url"
    }
  ],
  "selection_token": "site-selection-token-123"
}
```

### 2. Select Site
**Endpoint**: `POST /oauth/jira/select-site`
**Purpose**: Process user's site selection and complete OAuth flow

**Request Body**:
```json
{
  "selection_token": "site-selection-token-123",
  "selected_cloud_id": "cloud-id-1"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Site selected successfully",
  "redirect_url": "https://app.example.com/oauth/success"
}
```

### 3. Modified OAuth Callback
**Endpoint**: `GET /oauth/callback` (existing, modified)
**New Behavior**:
- After token exchange, check accessible resources
- If multiple sites: redirect to site selection UI
- If single site: proceed with normal flow

**New Response for Multiple Sites**:
```json
{
  "success": true,
  "requires_site_selection": true,
  "site_selection_url": "/oauth/jira/site-selection?state=xyz",
  "message": "Multiple sites available, please select one"
}
```

## Database Schema Changes

### Modified OAuthCredential Model
```python
class OAuthCredential(Base):
    # ... existing fields ...
    
    # NEW: Cloud ID for site-specific credentials
    cloud_id = Column(String, nullable=True, index=True)
    
    # MODIFIED: Composite key now includes cloud_id
    @classmethod
    def generate_composite_key(cls, user_id: str, tool_name: str, provider: str, cloud_id: str = None) -> str:
        if cloud_id and provider == "jira":
            return f"{user_id}_{tool_name}_{provider}_{cloud_id}"
        return f"{user_id}_{tool_name}_{provider}"
```

## Redis State Management

### Site Selection State
**Key**: `site_selection:{selection_token}`
**TTL**: 10 minutes
**Data**:
```json
{
  "user_id": "user123",
  "tool_name": "jira",
  "provider": "jira",
  "tokens": { /* OAuth tokens */ },
  "scopes": ["read:jira-work", "write:jira-work"],
  "accessible_sites": [ /* sites array */ ],
  "original_state_data": { /* original OAuth state */ },
  "created_at": "2024-01-01T00:00:00Z"
}
```

## Frontend Components

### Site Selection Page
- URL: `/oauth/jira/site-selection?state=xyz`
- Display available sites with names, URLs, and avatars
- Allow user to select one site
- Submit selection and redirect to success/failure page

## Implementation Steps

1. **Modify OAuth Service**: Add site detection logic
2. **Create Site Selection Endpoints**: New API routes
3. **Update Database Model**: Add cloud_id field
4. **Modify Composite Key Generation**: Include cloud_id for Jira
5. **Create Frontend Components**: Site selection UI
6. **Update jira-mcp**: Use site-specific credentials
7. **Add Migration**: Database schema update
8. **Testing**: End-to-end flow testing

## Backward Compatibility

- Existing single-site users: No change in behavior
- Existing credentials: Migration script to add cloud_id
- API responses: Maintain existing structure for single-site flows
