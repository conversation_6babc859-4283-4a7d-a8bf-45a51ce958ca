# Google Drive Sync Scalability - Deployment Guide

## 🚀 **Quick Start Implementation**

### **Phase 1: Immediate Improvements (2-3 days)**

#### **Step 1: Create Rate Limiter**
**File: `organisation-service/app/modules/connectors/handlers/gdrive/services/rate_limiter.py`**

```python
import time
import asyncio
from typing import Dict, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class RateLimitBucket:
    tokens: float
    last_refill: float
    max_tokens: float
    refill_rate: float

class AsyncRateLimiter:
    """
    Token bucket rate limiter for Google Drive API calls.
    """
    
    def __init__(self, requests_per_second: float = 10, burst_size: int = 20):
        self.requests_per_second = requests_per_second
        self.burst_size = burst_size
        self.buckets: Dict[str, RateLimitBucket] = {}
        self.global_bucket = RateLimitBucket(
            tokens=burst_size,
            last_refill=time.time(),
            max_tokens=burst_size,
            refill_rate=requests_per_second
        )
    
    async def acquire(self, user_id: Optional[str] = None) -> bool:
        """
        Acquire a token from the rate limiter.
        
        Args:
            user_id: Optional user ID for per-user limiting
            
        Returns:
            True if token acquired, False if rate limited
        """
        # Check global rate limit first
        if not self._acquire_from_bucket(self.global_bucket):
            return False
        
        # Check per-user rate limit if user_id provided
        if user_id:
            user_bucket = self._get_user_bucket(user_id)
            if not self._acquire_from_bucket(user_bucket):
                # Return token to global bucket
                self.global_bucket.tokens = min(
                    self.global_bucket.tokens + 1,
                    self.global_bucket.max_tokens
                )
                return False
        
        return True
    
    def _get_user_bucket(self, user_id: str) -> RateLimitBucket:
        """Get or create user-specific rate limit bucket."""
        if user_id not in self.buckets:
            self.buckets[user_id] = RateLimitBucket(
                tokens=self.burst_size // 2,  # Start with half capacity
                last_refill=time.time(),
                max_tokens=self.burst_size // 2,
                refill_rate=self.requests_per_second / 2  # Half the global rate per user
            )
        return self.buckets[user_id]
    
    def _acquire_from_bucket(self, bucket: RateLimitBucket) -> bool:
        """Acquire token from a specific bucket."""
        now = time.time()
        
        # Refill tokens based on time elapsed
        time_elapsed = now - bucket.last_refill
        tokens_to_add = time_elapsed * bucket.refill_rate
        bucket.tokens = min(bucket.tokens + tokens_to_add, bucket.max_tokens)
        bucket.last_refill = now
        
        # Try to acquire token
        if bucket.tokens >= 1.0:
            bucket.tokens -= 1.0
            return True
        
        return False
```

#### **Step 2: Enhanced Worker with Rate Limiting**
**Modify: `organisation-service/app/modules/connectors/handlers/gdrive/workers/sync_worker.py`**

Add these imports and modifications:

```python
# Add to imports
from ..services.rate_limiter import AsyncRateLimiter
import json
import time
from typing import Optional

# Modify the __init__ method
def __init__(self, worker_id: str = None, poll_interval=5):
    self.worker_id = worker_id or f"worker_{int(time.time())}"
    self.redis_service = RedisService()
    self.drive_service = GoogleDriveService()
    self.poll_interval = poll_interval
    self.running = False
    self.thread = None
    self.current_job = None
    self.jobs_processed = 0
    self.last_activity = time.time()
    
    # Add rate limiting
    self.rate_limiter = AsyncRateLimiter(
        requests_per_second=5,  # Per worker
        burst_size=10
    )

# Add these new methods to the class
def is_healthy(self) -> bool:
    """Check if worker is healthy."""
    if not self.running:
        return False
        
    # Check if worker has been inactive for too long
    if time.time() - self.last_activity > 300:  # 5 minutes
        return False
        
    return True

def _get_next_job_atomic(self) -> Optional[str]:
    """Atomically get and remove next job from queue."""
    try:
        # Use Redis Lua script for atomic operation
        lua_script = """
        local jobs = redis.call('ZRANGEBYSCORE', KEYS[1], '-inf', ARGV[1], 'LIMIT', 0, 1)
        if #jobs > 0 then
            redis.call('ZREM', KEYS[1], jobs[1])
            return jobs[1]
        else
            return nil
        end
        """
        
        result = self.redis_service.r_client.eval(
            lua_script, 
            1, 
            "gdrive_sync_queue", 
            str(time.time())
        )
        
        return result.decode('utf-8') if result else None
        
    except Exception as e:
        logger.error(f"Error getting next job: {e}")
        return None

# Modify the existing _run method
def _run(self):
    """Enhanced main worker loop with better error handling."""
    while self.running:
        try:
            # Use atomic operation to get and remove job
            job_id = self._get_next_job_atomic()
            if not job_id:
                time.sleep(self.poll_interval)
                continue
            
            self.current_job = job_id
            self.last_activity = time.time()
            
            # Get job data
            job_data_str = self.redis_service.get(job_id)
            if not job_data_str:
                logger.warning(f"Job {job_id} not found in Redis")
                continue
            
            job_data = json.loads(job_data_str)
            job_type = job_data.get('job_type')
            organisation_id = job_data.get('organisation_id')
            
            # Apply rate limiting
            if not asyncio.run(self.rate_limiter.acquire(organisation_id)):
                # Re-queue job for later
                score = time.time() + 60  # Retry in 1 minute
                self.redis_service.zadd("gdrive_sync_queue", {job_id: score})
                logger.info(f"Rate limited job {job_id}, re-queued")
                continue
            
            # Process the job based on type
            success = False
            if job_type == 'sync_drive':
                user_id = job_data.get('user_id')
                full_sync = job_data.get('full_sync', False)
                
                if not user_id:
                    logger.error(f"Invalid sync_drive job data - missing user_id: {job_data}")
                    continue
                
                logger.info(f"Processing Google Drive sync job for organization {organisation_id}")
                success, message, files_synced, folders_synced = self.drive_service.sync_drive(
                    organisation_id, full_sync
                )
                
                # Log the result
                if success:
                    logger.info(f"Google Drive sync completed for user {user_id}: {files_synced} files, {folders_synced} folders")
                    self.jobs_processed += 1
                else:
                    logger.error(f"Google Drive sync failed for user {user_id}: {message}")
                    
            elif job_type == 'sync_folders_by_ids':
                folder_ids = job_data.get('folder_ids', [])
                
                if not folder_ids:
                    logger.error(f"Invalid sync_folders_by_ids job data - missing folder_ids: {job_data}")
                    continue
                
                logger.info(f"Processing Google Drive folder sync job for organization {organisation_id}, folders: {folder_ids}")
                success, message, files_synced, folders_synced, synced_folders = self._process_folder_sync_job(
                    organisation_id, folder_ids
                )
                
                # Log the result
                if success:
                    logger.info(f"Google Drive folder sync completed for organization {organisation_id}: {files_synced} files, {folders_synced} folders, {len(synced_folders)} folders processed")
                    self.jobs_processed += 1
                else:
                    logger.error(f"Google Drive folder sync failed for organization {organisation_id}: {message}")
                    
                # Store the result in Redis for the gRPC service to retrieve
                result_key = f"folder_sync_result:{job_id}"
                result_data = {
                    'success': success,
                    'message': message,
                    'files_synced': files_synced,
                    'folders_synced': folders_synced,
                    'synced_folders': synced_folders,
                    'completed_at': datetime.utcnow().isoformat()
                }
                self.redis_service.set(result_key, json.dumps(result_data), ex=3600)  # 1 hour expiration
                
            else:
                logger.error(f"Unknown job type: {job_type}")
                continue
            
            self.current_job = None
            self.last_activity = time.time()
            
            # Delete the job
            self.redis_service.delete(job_id)
            
        except Exception as e:
            logger.error(f"Worker {self.worker_id} error: {e}")
            time.sleep(self.poll_interval)
```

#### **Step 3: Multiple Worker Instances**
**Create: `organisation-service/app/modules/connectors/handlers/gdrive/workers/multi_worker_launcher.py`**

```python
import logging
import threading
import time
from typing import List
import signal
import sys

from .sync_worker import GoogleDriveSyncWorker

logger = logging.getLogger(__name__)

class MultiWorkerLauncher:
    """
    Launches and manages multiple Google Drive sync workers.
    """
    
    def __init__(self, num_workers: int = 5):
        self.num_workers = num_workers
        self.workers: List[GoogleDriveSyncWorker] = []
        self.running = False
        
    def start(self):
        """Start all workers."""
        if self.running:
            return
            
        self.running = True
        
        # Create and start workers
        for i in range(self.num_workers):
            worker_id = f"gdrive_worker_{i}"
            worker = GoogleDriveSyncWorker(
                worker_id=worker_id,
                poll_interval=2  # Faster polling for better responsiveness
            )
            
            worker.start()
            self.workers.append(worker)
            logger.info(f"Started worker {worker_id}")
        
        logger.info(f"Started {self.num_workers} Google Drive sync workers")
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def stop(self):
        """Stop all workers."""
        if not self.running:
            return
            
        self.running = False
        
        logger.info("Stopping all workers...")
        for worker in self.workers:
            worker.stop()
        
        logger.info("All workers stopped")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)
    
    def get_stats(self) -> dict:
        """Get statistics for all workers."""
        stats = {
            'total_workers': len(self.workers),
            'active_workers': sum(1 for w in self.workers if w.running),
            'total_jobs_processed': sum(w.jobs_processed for w in self.workers),
            'workers': []
        }
        
        for worker in self.workers:
            worker_stats = {
                'worker_id': worker.worker_id,
                'running': worker.running,
                'current_job': worker.current_job,
                'jobs_processed': worker.jobs_processed,
                'last_activity': worker.last_activity
            }
            stats['workers'].append(worker_stats)
        
        return stats

# Entry point for running multiple workers
if __name__ == "__main__":
    import os
    
    # Get number of workers from environment variable
    num_workers = int(os.getenv('GDRIVE_WORKER_COUNT', 5))
    
    launcher = MultiWorkerLauncher(num_workers=num_workers)
    launcher.start()
    
    try:
        # Keep the main thread alive
        while True:
            time.sleep(60)
            stats = launcher.get_stats()
            logger.info(f"Worker stats: {stats}")
    except KeyboardInterrupt:
        launcher.stop()
```

## 🔧 **Configuration Updates**

### **Environment Variables**
Add to your `.env` file:

```bash
# Google Drive Sync Configuration
GDRIVE_WORKER_COUNT=5
GDRIVE_RATE_LIMIT_PER_SECOND=10
GDRIVE_RATE_LIMIT_BURST=20
GDRIVE_BATCH_SIZE=50

# Redis Configuration
REDIS_CONNECTION_POOL_SIZE=20
REDIS_MAX_CONNECTIONS=50

# Neo4j Configuration  
NEO4J_CONNECTION_POOL_SIZE=20
NEO4J_MAX_TRANSACTION_RETRY=3
```

### **Docker Compose Updates**
Add to your `docker-compose.yml`:

```yaml
services:
  gdrive-workers:
    build: ./organisation-service
    command: python -m app.modules.connectors.handlers.gdrive.workers.multi_worker_launcher
    environment:
      - GDRIVE_WORKER_COUNT=5
      - REDIS_HOST=redis
      - NEO4J_URI=bolt://neo4j:7687
    depends_on:
      - redis
      - neo4j
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

## 📊 **Testing the Implementation**

### **Load Testing Script**
**Create: `tests/load_test_gdrive_sync.py`**

```python
import asyncio
import aiohttp
import time
import json
from concurrent.futures import ThreadPoolExecutor
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def trigger_sync(session, user_id, org_id):
    """Trigger a sync for a user."""
    url = "http://localhost:8000/api/v1/google-drive/sync"
    payload = {
        "user_id": user_id,
        "organisation_id": org_id,
        "full_sync": False
    }
    
    start_time = time.time()
    try:
        async with session.post(url, json=payload) as response:
            result = await response.json()
            duration = time.time() - start_time
            
            logger.info(f"User {user_id}: {response.status} in {duration:.2f}s")
            return {
                'user_id': user_id,
                'status': response.status,
                'duration': duration,
                'success': result.get('success', False)
            }
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"User {user_id} failed: {e}")
        return {
            'user_id': user_id,
            'status': 500,
            'duration': duration,
            'success': False,
            'error': str(e)
        }

async def load_test(num_users=20):
    """Run load test with specified number of concurrent users."""
    logger.info(f"Starting load test with {num_users} concurrent users")
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        
        # Create sync tasks for multiple users
        for i in range(num_users):
            user_id = f"test_user_{i}"
            org_id = f"test_org_{i % 5}"  # 5 different orgs
            
            task = trigger_sync(session, user_id, org_id)
            tasks.append(task)
        
        # Execute all tasks concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_duration = time.time() - start_time
        
        # Analyze results
        successful = sum(1 for r in results if isinstance(r, dict) and r.get('success'))
        failed = len(results) - successful
        avg_duration = sum(r.get('duration', 0) for r in results if isinstance(r, dict)) / len(results)
        
        logger.info(f"""
        Load Test Results:
        - Total Users: {num_users}
        - Successful: {successful}
        - Failed: {failed}
        - Total Duration: {total_duration:.2f}s
        - Average Response Time: {avg_duration:.2f}s
        - Requests per Second: {num_users / total_duration:.2f}
        """)
        
        return results

if __name__ == "__main__":
    # Test with increasing load
    for num_users in [10, 20, 50, 100]:
        print(f"\n{'='*50}")
        print(f"Testing with {num_users} concurrent users")
        print(f"{'='*50}")
        
        asyncio.run(load_test(num_users))
        
        # Wait between tests
        time.sleep(30)
```

## 🚀 **Deployment Steps**

### **1. Deploy Phase 1 (Immediate)**
```bash
# 1. Add the new files
cp rate_limiter.py organisation-service/app/modules/connectors/handlers/gdrive/services/
cp multi_worker_launcher.py organisation-service/app/modules/connectors/handlers/gdrive/workers/

# 2. Update existing worker
# Apply the modifications to sync_worker.py

# 3. Update environment variables
echo "GDRIVE_WORKER_COUNT=5" >> .env

# 4. Restart services
docker-compose restart gdrive-workers
```

### **2. Monitor Performance**
```bash
# Check worker stats
curl http://localhost:8000/api/v1/google-drive/worker-stats

# Monitor Redis queue
redis-cli -h localhost -p 6379 ZCARD gdrive_sync_queue

# Check logs
docker-compose logs -f gdrive-workers
```

### **3. Scale Up Gradually**
```bash
# Increase workers gradually
export GDRIVE_WORKER_COUNT=10
docker-compose up -d --scale gdrive-workers=2
```

This implementation will immediately improve your system's ability to handle concurrent users while maintaining backward compatibility. The full worker pool implementation can be added in Phase 2 for even better scalability.
