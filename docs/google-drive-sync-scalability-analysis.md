# Google Drive Sync Scalability Analysis & Recommendations

## Current Architecture Issues

### 🚨 **Critical Bottlenecks Identified**

#### 1. **Single Worker Thread Architecture**

- **Current**: One `GoogleDriveSyncWorker` thread processes all sync jobs sequentially
- **Problem**: Cannot handle concurrent users - jobs are processed one at a time
- **Impact**: 100 concurrent users would create a massive queue with hours of delay

#### 2. **Tight Coupling & Synchronous Operations**

- **Current**: Direct database writes during sync operations
- **Problem**: Each file/folder operation blocks the worker thread
- **Impact**: Long-running operations prevent other users from being processed

#### 3. **Database Connection Bottlenecks**

- **Current**: Single Neo4j connection per operation
- **Problem**: No connection pooling for concurrent operations
- **Impact**: Database becomes the bottleneck under load

#### 4. **Memory & Resource Inefficiency**

- **Current**: Full folder tree loaded into memory during sync
- **Problem**: Large Google Drive accounts consume excessive memory
- **Impact**: Server crashes or OOM errors with multiple large syncs

#### 5. **No Rate Limiting or Backpressure**

- **Current**: No limits on concurrent sync operations
- **Problem**: Google Drive API rate limits will be exceeded
- **Impact**: API failures and sync failures for all users

## 🎯 **Recommended Scalable Architecture**

### **1. Multi-Worker Pool Architecture**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  Job Scheduler   │    │  Worker Pool    │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ Rate Limiter│ │    │ │ Priority     │ │    │ │ Worker 1    │ │
│ └─────────────┘ │    │ │ Queue        │ │    │ └─────────────┘ │
│                 │    │ └──────────────┘ │    │ ┌─────────────┐ │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ │ Worker 2    │ │
│ │ Job Creator │ │────▶│ │ Redis Queue  │ │────▶│ └─────────────┘ │
│ └─────────────┘ │    │ └──────────────┘ │    │ ┌─────────────┐ │
└─────────────────┘    └──────────────────┘    │ │ Worker N    │ │
                                               │ └─────────────┘ │
                                               └─────────────────┘
```

### **2. Microservice-Based Sync Architecture**

#### **Core Components:**

1. **Sync Orchestrator Service**

   - Manages sync workflows
   - Handles job prioritization
   - Monitors worker health

2. **Worker Pool Manager**

   - Dynamically scales workers based on load
   - Manages worker lifecycle
   - Handles worker failures

3. **Batch Processing Engine**

   - Processes files/folders in batches
   - Optimizes database operations
   - Reduces API calls

4. **Progress Tracking Service**
   - Real-time sync progress updates
   - User notifications
   - Error reporting

### **3. Database Optimization Strategy**

#### **Connection Pooling & Transactions**

```python
# Enhanced Neo4j connection management
class Neo4jConnectionPool:
    def __init__(self, max_connections=50):
        self.pool = ConnectionPool(max_size=max_connections)
        self.batch_size = 100

    async def batch_write_files(self, files_batch):
        async with self.pool.acquire() as conn:
            async with conn.begin_transaction() as tx:
                # Batch insert 100 files at once
                await tx.run(BATCH_INSERT_QUERY, files_batch)
```

#### **Optimized Queries**

- Replace individual file operations with batch operations
- Use UNWIND for bulk inserts
- Implement proper indexing strategy
- Add query result caching

### **4. Google Drive API Optimization**

#### **Rate Limiting & Backpressure**

```python
class GoogleDriveAPIManager:
    def __init__(self):
        self.rate_limiter = AsyncRateLimiter(
            requests_per_second=10,  # Per user
            burst_size=20
        )
        self.global_limiter = AsyncRateLimiter(
            requests_per_second=100,  # Global
            burst_size=200
        )
```

#### **Intelligent Pagination**

- Dynamic page size based on API response times
- Parallel processing of pages
- Resume capability for interrupted syncs

## 📋 **Implementation Plan**

### **Phase 1: Worker Pool Implementation**

#### **Files to Modify:**

1. **`organisation-service/app/modules/connectors/handlers/gdrive/workers/`**

   - Create `worker_pool_manager.py`
   - Create `sync_orchestrator.py`
   - Modify `sync_worker.py` to support multiple instances

2. **`organisation-service/app/modules/connectors/handlers/gdrive/services/google_drive_service.py`**

   - Add batch processing methods
   - Implement connection pooling
   - Add progress tracking

3. **`organisation-service/app/services/neo4j_service.py`**
   - Add connection pooling
   - Implement batch operations
   - Add transaction management

#### **New Components to Create:**

```
organisation-service/
├── app/modules/connectors/handlers/gdrive/
│   ├── workers/
│   │   ├── worker_pool_manager.py      # NEW
│   │   ├── sync_orchestrator.py        # NEW
│   │   ├── batch_processor.py          # NEW
│   │   └── progress_tracker.py         # NEW
│   ├── services/
│   │   ├── rate_limiter.py             # NEW
│   │   └── connection_pool.py          # NEW
│   └── config/
│       └── worker_config.py            # NEW
```

### **Phase 2: Database Optimization**

#### **Files to Modify:**

1. **`organisation-service/app/db/neo4j.py`**

   - Implement connection pooling
   - Add async support
   - Add batch operation support

2. **`organisation-service/app/modules/connectors/handlers/gdrive/repository/google_drive_queries.py`**
   - Add batch query methods
   - Optimize existing queries
   - Add indexing queries

### **Phase 3: API Gateway Enhancement**

#### **Files to Modify:**

1. **`api-gateway/app/api/routers/google_drive_routes.py`**

   - Add rate limiting middleware
   - Add progress tracking endpoints
   - Add sync status endpoints

2. **`api-gateway/app/services/google_drive_service.py`**
   - Add async job submission
   - Add progress polling
   - Add error handling

## 🔧 **Configuration Changes**

### **Environment Variables to Add:**

```bash
# Worker Pool Configuration
GDRIVE_WORKER_POOL_SIZE=10
GDRIVE_MAX_CONCURRENT_SYNCS=50
GDRIVE_BATCH_SIZE=100

# Database Configuration
NEO4J_CONNECTION_POOL_SIZE=50
NEO4J_MAX_TRANSACTION_RETRY=3
NEO4J_BATCH_SIZE=100

# Rate Limiting
GDRIVE_API_RATE_LIMIT_PER_USER=10
GDRIVE_API_GLOBAL_RATE_LIMIT=100
GDRIVE_API_BURST_SIZE=20

# Redis Configuration
REDIS_CONNECTION_POOL_SIZE=20
REDIS_MAX_CONNECTIONS=100
```

### **Docker Compose Updates:**

```yaml
# Add to docker-compose.yml
services:
  gdrive-worker-pool:
    build: ./organisation-service
    command: python -m app.modules.connectors.handlers.gdrive.workers.worker_pool_manager
    environment:
      - WORKER_POOL_SIZE=10
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - neo4j
    deploy:
      replicas: 3 # Scale horizontally
```

## 📊 **Performance Targets**

### **Scalability Goals:**

- **100 concurrent users**: < 30 seconds average sync time
- **1000 files per user**: < 2 minutes sync time
- **API rate limits**: Never exceeded
- **Memory usage**: < 2GB per worker
- **Database connections**: < 50 concurrent

### **Monitoring Metrics:**

- Sync queue length
- Worker utilization
- Database connection pool usage
- API rate limit consumption
- Memory usage per worker
- Sync success/failure rates

## 🚀 **Deployment Strategy**

### **Rolling Deployment:**

1. Deploy new worker pool alongside existing worker
2. Gradually migrate traffic to new system
3. Monitor performance and error rates
4. Complete migration once stable
5. Remove old single-worker system

### **Rollback Plan:**

- Keep existing single worker as fallback
- Feature flags to switch between systems
- Database schema backward compatibility
- Redis queue compatibility

## 💡 **Additional Optimizations**

### **Caching Strategy:**

- Redis cache for folder structures
- In-memory cache for frequently accessed files
- CDN for file metadata

### **Monitoring & Alerting:**

- Prometheus metrics
- Grafana dashboards
- PagerDuty alerts for failures
- Real-time sync progress for users

This architecture will handle 100+ concurrent users efficiently while maintaining data consistency and providing excellent user experience.

## 🛠️ **Detailed Implementation Guide**

### **Step 1: Create Worker Pool Manager**

**File: `organisation-service/app/modules/connectors/handlers/gdrive/workers/worker_pool_manager.py`**

```python
import asyncio
import logging
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Optional
import threading
import time
from dataclasses import dataclass

from app.utils.redis.redis_service import RedisService
from .sync_worker import GoogleDriveSyncWorker

logger = logging.getLogger(__name__)

@dataclass
class WorkerStats:
    worker_id: str
    is_active: bool
    current_job: Optional[str]
    jobs_processed: int
    last_activity: float

class WorkerPoolManager:
    """
    Manages a pool of Google Drive sync workers for concurrent processing.
    """

    def __init__(self, pool_size: int = 10, max_queue_size: int = 1000):
        self.pool_size = pool_size
        self.max_queue_size = max_queue_size
        self.workers: Dict[str, GoogleDriveSyncWorker] = {}
        self.worker_stats: Dict[str, WorkerStats] = {}
        self.redis_service = RedisService()
        self.running = False
        self.monitor_thread = None

    async def start(self):
        """Start the worker pool."""
        if self.running:
            return

        self.running = True

        # Create worker instances
        for i in range(self.pool_size):
            worker_id = f"gdrive_worker_{i}"
            worker = GoogleDriveSyncWorker(
                worker_id=worker_id,
                poll_interval=1  # Faster polling for better responsiveness
            )

            self.workers[worker_id] = worker
            self.worker_stats[worker_id] = WorkerStats(
                worker_id=worker_id,
                is_active=False,
                current_job=None,
                jobs_processed=0,
                last_activity=time.time()
            )

            # Start worker in separate thread
            worker.start()

        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=self._monitor_workers)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

        logger.info(f"Started worker pool with {self.pool_size} workers")

    async def stop(self):
        """Stop the worker pool."""
        self.running = False

        # Stop all workers
        for worker in self.workers.values():
            worker.stop()

        # Stop monitoring
        if self.monitor_thread:
            self.monitor_thread.join(timeout=30)

        logger.info("Worker pool stopped")

    def _monitor_workers(self):
        """Monitor worker health and restart failed workers."""
        while self.running:
            try:
                for worker_id, worker in self.workers.items():
                    if not worker.is_healthy():
                        logger.warning(f"Worker {worker_id} is unhealthy, restarting...")
                        worker.stop()

                        # Create new worker instance
                        new_worker = GoogleDriveSyncWorker(
                            worker_id=worker_id,
                            poll_interval=1
                        )
                        new_worker.start()
                        self.workers[worker_id] = new_worker

                        # Reset stats
                        self.worker_stats[worker_id] = WorkerStats(
                            worker_id=worker_id,
                            is_active=False,
                            current_job=None,
                            jobs_processed=0,
                            last_activity=time.time()
                        )

                time.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"Error in worker monitoring: {e}")
                time.sleep(10)

    def get_pool_stats(self) -> Dict:
        """Get current pool statistics."""
        active_workers = sum(1 for stats in self.worker_stats.values() if stats.is_active)
        total_jobs_processed = sum(stats.jobs_processed for stats in self.worker_stats.values())

        # Get queue length from Redis
        queue_length = self.redis_service.zcard("gdrive_sync_queue")

        return {
            "pool_size": self.pool_size,
            "active_workers": active_workers,
            "total_jobs_processed": total_jobs_processed,
            "queue_length": queue_length,
            "worker_stats": [
                {
                    "worker_id": stats.worker_id,
                    "is_active": stats.is_active,
                    "current_job": stats.current_job,
                    "jobs_processed": stats.jobs_processed,
                    "last_activity": stats.last_activity
                }
                for stats in self.worker_stats.values()
            ]
        }

# Global worker pool instance
worker_pool_manager = WorkerPoolManager()
```

### **Step 2: Enhanced Sync Worker**

**Modifications to: `organisation-service/app/modules/connectors/handlers/gdrive/workers/sync_worker.py`**

```python
# Add these methods to the existing GoogleDriveSyncWorker class

class GoogleDriveSyncWorker:
    def __init__(self, worker_id: str = None, poll_interval=5):
        self.worker_id = worker_id or f"worker_{int(time.time())}"
        self.redis_service = RedisService()
        self.drive_service = GoogleDriveService()
        self.poll_interval = poll_interval
        self.running = False
        self.thread = None
        self.current_job = None
        self.jobs_processed = 0
        self.last_activity = time.time()

        # Add rate limiting
        self.rate_limiter = AsyncRateLimiter(
            requests_per_second=5,  # Per worker
            burst_size=10
        )

    def is_healthy(self) -> bool:
        """Check if worker is healthy."""
        if not self.running:
            return False

        # Check if worker has been inactive for too long
        if time.time() - self.last_activity > 300:  # 5 minutes
            return False

        return True

    def _run(self):
        """Enhanced main worker loop with better error handling."""
        while self.running:
            try:
                # Use atomic operation to get and remove job
                job_id = self._get_next_job_atomic()
                if not job_id:
                    time.sleep(self.poll_interval)
                    continue

                self.current_job = job_id
                self.last_activity = time.time()

                # Process job with timeout
                success = self._process_job_with_timeout(job_id)

                if success:
                    self.jobs_processed += 1

                self.current_job = None
                self.last_activity = time.time()

            except Exception as e:
                logger.error(f"Worker {self.worker_id} error: {e}")
                time.sleep(self.poll_interval)

    def _get_next_job_atomic(self) -> Optional[str]:
        """Atomically get and remove next job from queue."""
        try:
            # Use Redis Lua script for atomic operation
            lua_script = """
            local jobs = redis.call('ZRANGEBYSCORE', KEYS[1], '-inf', ARGV[1], 'LIMIT', 0, 1)
            if #jobs > 0 then
                redis.call('ZREM', KEYS[1], jobs[1])
                return jobs[1]
            else
                return nil
            end
            """

            result = self.redis_service.r_client.eval(
                lua_script,
                1,
                "gdrive_sync_queue",
                str(time.time())
            )

            return result.decode('utf-8') if result else None

        except Exception as e:
            logger.error(f"Error getting next job: {e}")
            return None

    def _process_job_with_timeout(self, job_id: str, timeout: int = 1800) -> bool:
        """Process job with timeout (30 minutes default)."""
        try:
            # Get job data
            job_data_str = self.redis_service.get(job_id)
            if not job_data_str:
                logger.warning(f"Job {job_id} not found")
                return False

            job_data = json.loads(job_data_str)
            job_type = job_data.get('job_type')
            organisation_id = job_data.get('organisation_id')

            # Apply rate limiting
            if not self.rate_limiter.acquire():
                # Re-queue job for later
                score = time.time() + 60  # Retry in 1 minute
                self.redis_service.zadd("gdrive_sync_queue", {job_id: score})
                return False

            # Process based on job type
            if job_type == 'sync_drive':
                return self._process_sync_drive_job(job_data, organisation_id)
            elif job_type == 'sync_folders_by_ids':
                return self._process_folder_sync_job(job_data, organisation_id)
            else:
                logger.error(f"Unknown job type: {job_type}")
                return False

        except Exception as e:
            logger.error(f"Error processing job {job_id}: {e}")
            return False
        finally:
            # Always clean up job
            self.redis_service.delete(job_id)
```

### **Step 3: Batch Processing Engine**

**File: `organisation-service/app/modules/connectors/handlers/gdrive/workers/batch_processor.py`**

```python
import asyncio
import logging
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
import time

from app.services.neo4j_service import execute_write_query
from app.modules.connectors.handlers.gdrive.repository.google_drive_queries import (
    GoogleDriveFileQueries,
    GoogleDriveFolderQueries
)

logger = logging.getLogger(__name__)

@dataclass
class BatchItem:
    item_type: str  # 'file' or 'folder'
    data: Dict[str, Any]
    relationships: List[Dict[str, Any]]

class BatchProcessor:
    """
    Processes Google Drive files and folders in batches for better performance.
    """

    def __init__(self, batch_size: int = 100):
        self.batch_size = batch_size
        self.file_queries = GoogleDriveFileQueries()
        self.folder_queries = GoogleDriveFolderQueries()

    async def process_items_batch(
        self,
        items: List[BatchItem],
        organisation_id: str
    ) -> Tuple[int, int]:
        """
        Process a batch of files and folders.

        Returns:
            Tuple of (files_processed, folders_processed)
        """
        files_batch = []
        folders_batch = []
        file_relationships = []
        folder_relationships = []

        # Separate files and folders
        for item in items:
            if item.item_type == 'file':
                files_batch.append(item.data)
                file_relationships.extend(item.relationships)
            elif item.item_type == 'folder':
                folders_batch.append(item.data)
                folder_relationships.extend(item.relationships)

        files_processed = 0
        folders_processed = 0

        try:
            # Process folders first (they might be parents of files)
            if folders_batch:
                folders_processed = await self._batch_create_folders(
                    folders_batch, organisation_id
                )

            # Process files
            if files_batch:
                files_processed = await self._batch_create_files(
                    files_batch, organisation_id
                )

            # Create relationships
            if folder_relationships:
                await self._batch_create_folder_relationships(folder_relationships)

            if file_relationships:
                await self._batch_create_file_relationships(file_relationships)

            logger.info(
                f"Batch processed: {files_processed} files, "
                f"{folders_processed} folders for org {organisation_id}"
            )

        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            raise

        return files_processed, folders_processed

    async def _batch_create_files(
        self,
        files_data: List[Dict[str, Any]],
        organisation_id: str
    ) -> int:
        """Create files in batch."""
        try:
            # Prepare batch data
            batch_params = {
                'files': [
                    {
                        'id': file_data['id'],
                        'properties': {
                            **file_data,
                            'organisation_id': organisation_id,
                            'created_in_neo4j': time.time(),
                            'source': 'google_drive'
                        }
                    }
                    for file_data in files_data
                ],
                'organisation_id': organisation_id
            }

            # Execute batch query
            query = """
            UNWIND $files as file_data
            MERGE (f:GoogleDriveFile {id: file_data.id})
            SET f += file_data.properties
            SET f.organisation_id = $organisation_id
            """

            execute_write_query(query, batch_params)
            return len(files_data)

        except Exception as e:
            logger.error(f"Error in batch file creation: {e}")
            raise

    async def _batch_create_folders(
        self,
        folders_data: List[Dict[str, Any]],
        organisation_id: str
    ) -> int:
        """Create folders in batch."""
        try:
            # Similar to files but for folders
            batch_params = {
                'folders': [
                    {
                        'id': folder_data['id'],
                        'properties': {
                            **folder_data,
                            'organisation_id': organisation_id,
                            'created_in_neo4j': time.time(),
                            'source': 'google_drive'
                        }
                    }
                    for folder_data in folders_data
                ],
                'organisation_id': organisation_id
            }

            query = """
            UNWIND $folders as folder_data
            MERGE (f:GoogleDriveFolder {id: folder_data.id})
            SET f += folder_data.properties
            SET f.organisation_id = $organisation_id
            """

            execute_write_query(query, batch_params)
            return len(folders_data)

        except Exception as e:
            logger.error(f"Error in batch folder creation: {e}")
            raise

    async def _batch_create_file_relationships(
        self,
        relationships: List[Dict[str, Any]]
    ):
        """Create file-folder relationships in batch."""
        if not relationships:
            return

        try:
            query = """
            UNWIND $relationships as rel
            MATCH (folder:GoogleDriveFolder {id: rel.folder_id})
            MATCH (file:GoogleDriveFile {id: rel.file_id})
            WHERE folder.organisation_id = file.organisation_id
            MERGE (folder)-[:CONTAINS]->(file)
            """

            execute_write_query(query, {'relationships': relationships})

        except Exception as e:
            logger.error(f"Error creating file relationships: {e}")
            raise

    async def _batch_create_folder_relationships(
        self,
        relationships: List[Dict[str, Any]]
    ):
        """Create folder-folder relationships in batch."""
        if not relationships:
            return

        try:
            query = """
            UNWIND $relationships as rel
            MATCH (parent:GoogleDriveFolder {id: rel.parent_id})
            MATCH (child:GoogleDriveFolder {id: rel.child_id})
            WHERE parent.organisation_id = child.organisation_id
            MERGE (parent)-[:CONTAINS]->(child)
            """

            execute_write_query(query, {'relationships': relationships})

        except Exception as e:
            logger.error(f"Error creating folder relationships: {e}")
            raise
```

This implementation provides:

1. **Concurrent Processing**: Multiple workers process jobs simultaneously
2. **Batch Operations**: Database operations are batched for efficiency
3. **Rate Limiting**: Prevents API quota exhaustion
4. **Health Monitoring**: Automatic worker restart on failures
5. **Atomic Operations**: Redis Lua scripts prevent race conditions
6. **Progress Tracking**: Real-time statistics and monitoring

The system can easily handle 100+ concurrent users with proper resource allocation.
