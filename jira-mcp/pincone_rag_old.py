import json
import logging
import os
from typing import Any, Dict, List, Optional

import pinecone
import redis
from dotenv import load_dotenv
from neo4j import GraphDatabase
from sentence_transformers import SentenceTransformer
from transformers import pipeline

# Load environment variables
load_dotenv(override=True)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Neo4j
NEO4J_URI = os.getenv("NEO4J_URI")
NEO4J_USER = os.getenv("NEO4J_USER")
NEO4J_PASS = os.getenv("NEO4J_PASS")

# Initialize Pinecone
pc = pinecone.Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
index_name = "agent-index"
if index_name not in pc.list_indexes():
    pc.create_index(
        name=index_name,
        dimension=384,
        metric="cosine",
        spec=pinecone.ServerlessSpec(cloud="aws", region="us-west-2"),
    )
index = pc.Index(index_name)

# Initialize embedding model
embedder = SentenceTransformer("all-MiniLM-L6-v2")

# Initialize Redis for caching
redis_client = redis.Redis(host="localhost", port=6379, db=0)


class KnowledgeGraph:
    def __init__(self, uri: str, username: str, password: str):
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        self.verify_connectivity()

    def close(self):
        self.driver.close()

    def verify_connectivity(self):
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 1")
                logger.info("Successfully connected to Neo4j database")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise

    def clear_database(self):
        with self.driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
            logger.info("Database cleared")

    def create_constraints(self):
        with self.driver.session() as session:
            constraints = [
                "CREATE CONSTRAINT agent_id IF NOT EXISTS FOR (a:Agent) REQUIRE a.id IS UNIQUE",
                "CREATE CONSTRAINT mcp_id IF NOT EXISTS FOR (m:MCP) REQUIRE m.id IS UNIQUE",
                "CREATE CONSTRAINT tool_id IF NOT EXISTS FOR (t:Tool) REQUIRE t.id IS UNIQUE",
            ]
            for constraint in constraints:
                try:
                    session.run(constraint)
                    logger.info(f"Created constraint: {constraint.split()[2]}")
                except Exception as e:
                    logger.debug(f"Constraint may already exist: {e}")

    def create_agent(
        self, id: str, name: str, description: str, **kwargs
    ) -> Dict[str, Any]:
        with self.driver.session() as session:
            properties = {"id": id, "name": name, "description": description}
            properties.update(kwargs)
            result = session.run(
                """
                CREATE (a:Agent $props)
                RETURN a
                """,
                props=properties,
            )
            record = result.single()
            logger.info(f"Created agent: {name} ({id})")
            return dict(record["a"])

    def get_agent(self, id: str) -> Optional[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (a:Agent {id: $id}) RETURN a", id=id)
            record = result.single()
            return dict(record["a"]) if record else None

    def get_all_agents(self) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (a:Agent) RETURN a ORDER BY a.name")
            return [dict(record["a"]) for record in result]

    def create_mcp(
        self, id: str, name: str, description: str, agent_id: str, **kwargs
    ) -> Dict[str, Any]:
        with self.driver.session() as session:
            tx = session.begin_transaction()
            try:
                agent_check = tx.run(
                    "MATCH (a:Agent {id: $agent_id}) RETURN a", agent_id=agent_id
                )
                if not agent_check.single():
                    raise ValueError(f"Agent with id '{agent_id}' does not exist")
                properties = {"id": id, "name": name, "description": description}
                properties.update(kwargs)
                result = tx.run(
                    """
                    MATCH (a:Agent {id: $agent_id})
                    CREATE (m:MCP $props)
                    CREATE (a)-[:ACCESSES_MCP]->(m)
                    RETURN m, a
                    """,
                    agent_id=agent_id,
                    props=properties,
                )
                record = result.single()
                tx.commit()
                logger.info(
                    f"Created MCP: {name} ({id}) and linked to agent: {agent_id}"
                )
                return {
                    "mcp": dict(record["m"]),
                    "relationship_created": True,
                    "linked_agent": agent_id,
                }
            except Exception as e:
                tx.rollback()
                logger.error(f"Failed to create MCP {name} ({id}): {e}")
                raise

    def get_mcp(self, id: str) -> Optional[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (m:MCP {id: $id}) RETURN m", id=id)
            record = result.single()
            return dict(record["m"]) if record else None

    def get_all_mcps(self) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (m:MCP) RETURN m ORDER BY m.name")
            return [dict(record["m"]) for record in result]

    def create_tool(
        self, id: str, name: str, description: str, agent_id: str, **kwargs
    ) -> Dict[str, Any]:
        with self.driver.session() as session:
            tx = session.begin_transaction()
            try:
                agent_check = tx.run(
                    "MATCH (a:Agent {id: $agent_id}) RETURN a", agent_id=agent_id
                )
                if not agent_check.single():
                    raise ValueError(f"Agent with id '{agent_id}' does not exist")
                properties = {"id": id, "name": name, "description": description}
                properties.update(kwargs)
                result = tx.run(
                    """
                    MATCH (a:Agent {id: $agent_id})
                    CREATE (t:Tool $props)
                    CREATE (a)-[:USES_TOOL]->(t)
                    RETURN t, a
                    """,
                    agent_id=agent_id,
                    props=properties,
                )
                record = result.single()
                tx.commit()
                logger.info(
                    f"Created tool: {name} ({id}) and linked to agent: {agent_id}"
                )
                return {
                    "tool": dict(record["t"]),
                    "relationship_created": True,
                    "linked_agent": agent_id,
                }
            except Exception as e:
                tx.rollback()
                logger.error(f"Failed to create tool {name} ({id}): {e}")
                raise

    def get_tool(self, id: str) -> Optional[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (t:Tool {id: $id}) RETURN t", id=id)
            record = result.single()
            return dict(record["t"]) if record else None

    def get_all_tools(self) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (t:Tool) RETURN t ORDER BY t.name")
            return [dict(record["t"]) for record in result]

    def create_uses_tool_relationship(self, agent_id: str, tool_id: str, **kwargs):
        with self.driver.session() as session:
            session.run(
                """
                MATCH (a:Agent {id: $agent_id})
                MATCH (t:Tool {id: $tool_id})
                CREATE (a)-[:USES_TOOL $props]->(t)
                """,
                agent_id=agent_id,
                tool_id=tool_id,
                props=kwargs,
            )
            logger.info(f"Created USES_TOOL relationship: {agent_id} -> {tool_id}")

    def create_accesses_mcp_relationship(self, agent_id: str, mcp_id: str, **kwargs):
        with self.driver.session() as session:
            session.run(
                """
                MATCH (a:Agent {id: $agent_id})
                MATCH (m:MCP {id: $mcp_id})
                CREATE (a)-[:ACCESSES_MCP $props]->(m)
                """,
                agent_id=agent_id,
                mcp_id=mcp_id,
                props=kwargs,
            )
            logger.info(f"Created ACCESSES_MCP relationship: {agent_id} -> {mcp_id}")

    def find_agents_by_tool(self, tool_id: str) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (a:Agent)-[r:USES_TOOL]->(t:Tool {id: $tool_id})
                RETURN a.id as agent_id, a.name as agent_name, a.description as agent_description,
                       r as relationship_props
                """,
                tool_id=tool_id,
            )
            return [dict(record) for record in result]

    def find_tools_by_agent(self, agent_id: str) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (a:Agent {id: $agent_id})-[r:USES_TOOL]->(t:Tool)
                RETURN t.id as tool_id, t.name as tool_name, t.description as tool_description,
                       r as relationship_props
                """,
                agent_id=agent_id,
            )
            return [dict(record) for record in result]

    def find_mcps_by_agent(self, agent_id: str) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (a:Agent {id: $agent_id})-[r:ACCESSES_MCP]->(m:MCP)
                RETURN m.id as mcp_id, m.name as mcp_name, m.description as mcp_description,
                       r as relationship_props
                """,
                agent_id=agent_id,
            )
            return [dict(record) for record in result]

    def get_entity_relationships(
        self, entity_type: str, entity_id: str
    ) -> Dict[str, Any]:
        with self.driver.session() as session:
            result = session.run(
                f"""
                MATCH (e:{entity_type} {{id: $entity_id}})
                OPTIONAL MATCH (e)-[r1]->(connected1)
                OPTIONAL MATCH (e)<-[r2]-(connected2)
                RETURN e,
                       collect(DISTINCT {{type: type(r1), target: connected1, relationship: r1}}) as outgoing,
                       collect(DISTINCT {{type: type(r2), source: connected2, relationship: r2}}) as incoming
                """,
                entity_id=entity_id,
            )
            record = result.single()
            if not record:
                return {"error": f"{entity_type} not found"}
            return {
                "entity": dict(record["e"]),
                "outgoing_relationships": [
                    r for r in record["outgoing"] if r["type"] is not None
                ],
                "incoming_relationships": [
                    r for r in record["incoming"] if r["type"] is not None
                ],
            }


def index_agents_to_pinecone(kg: KnowledgeGraph):
    agents = kg.get_all_agents()
    vectors = []
    for agent in agents:
        metadata_text = f"{agent['name']} {agent['role']} {agent['description']} {' '.join(agent.get('capabilities', []))}"
        embedding = embedder.encode(metadata_text).tolist()
        vectors.append(
            (
                agent["id"],
                embedding,
                {
                    "name": agent["name"],
                    "role": agent["role"],
                    "description": agent["description"],
                    "capabilities": agent.get("capabilities", []),
                },
            )
        )
    index.upsert(vectors=vectors)
    logger.info("Agents indexed in Pinecone")


def create_sample_knowledge_graph(kg: KnowledgeGraph):
    kg.clear_database()
    kg.create_constraints()

    # Agents (1-4)
    kg.create_agent(
        "agent-001",
        "Data Analyst Agent",
        "Analyzes data and generates insights",
        role="Data Analyst",
        capabilities=["data_analysis", "visualization", "reporting"],
    )
    kg.create_agent(
        "agent-002",
        "Code Assistant",
        "Helps with programming tasks",
        role="Developer",
        capabilities=["code_generation", "debugging", "testing"],
    )
    kg.create_agent(
        "agent-003",
        "Project Manager Agent",
        "Manages project workflows and timelines",
        role="Project Manager",
        capabilities=["task_management", "jira_workflows", "planning"],
    )
    kg.create_agent(
        "agent-004",
        "DevOps Agent",
        "Handles CI/CD and infrastructure",
        role="DevOps Engineer",
        capabilities=["ci_cd", "deployment", "monitoring"],
    )

    # MCPs (1-3)
    kg.create_mcp(
        "mcp-001",
        "Database MCP",
        "Provides access to SQL databases",
        "agent-001",
        data_source="postgresql",
        protocol_version="1.0",
    )
    kg.create_mcp(
        "mcp-002",
        "File System MCP",
        "Provides access to file system operations",
        "agent-002",
        data_source="local_files",
        protocol_version="1.0",
    )
    kg.create_mcp(
        "mcp-003",
        "API Gateway MCP",
        "Provides access to external APIs",
        "agent-003",
        data_source="rest_apis",
        protocol_version="1.0",
    )

    # Tools (1-8)
    kg.create_tool(
        "tool-001",
        "SQL Query Tool",
        "Executes SQL queries against databases",
        "agent-001",
        function_name="execute_sql",
        parameters=["query", "database"],
    )
    kg.create_tool(
        "tool-002",
        "File Reader Tool",
        "Reads files from the file system",
        "agent-002",
        function_name="read_file",
        parameters=["file_path"],
    )
    kg.create_tool(
        "tool-003",
        "Chart Generator",
        "Creates charts and visualizations",
        "agent-001",
        function_name="create_chart",
        parameters=["data", "chart_type"],
    )
    kg.create_tool(
        "tool-004",
        "Web Search Tool",
        "Searches the web for information",
        "agent-003",
        function_name="web_search",
        parameters=["query", "max_results"],
    )
    kg.create_tool(
        "tool-005",
        "Code Reviewer",
        "Reviews and suggests code improvements",
        "agent-002",
        function_name="review_code",
        parameters=["code_snippet"],
    )
    kg.create_tool(
        "tool-006",
        "Report Generator",
        "Generates detailed reports",
        "agent-001",
        function_name="generate_report",
        parameters=["data", "format"],
    )
    kg.create_tool(
        "tool-007",
        "Jira Task Creator",
        "Creates tasks in Jira",
        "agent-003",
        function_name="create_jira_task",
        parameters=["task_details"],
    )
    kg.create_tool(
        "tool-008",
        "CI/CD Pipeline Tool",
        "Manages CI/CD pipelines",
        "agent-004",
        function_name="manage_pipeline",
        parameters=["pipeline_config"],
    )

    logger.info("Sample knowledge graph created successfully!")


def has_required_knowledge(agent_caps: List[str], query: str) -> bool:
    query_lower = query.lower()
    return any(keyword in query_lower for keyword in agent_caps)


def find_relevant_agents(query: str, top_k: int = 1) -> List[Dict[str, Any]]:
    if not query or not isinstance(query, str):
        return []
    try:
        query_embedding = embedder.encode(query).tolist()
        results = index.query(
            queries=[query_embedding], top_k=top_k, include_metadata=True
        )
        return [
            {
                "id": match["id"],
                "name": match["metadata"]["name"],
                "role": match["metadata"]["role"],
                "description": match["metadata"]["description"],
                "score": match["score"],
            }
            for match in results["results"][0]["matches"]
        ]
    except Exception as e:
        logger.error(f"Error querying Pinecone: {e}")
        return []


def get_agent_details(kg: KnowledgeGraph, agent_id: str) -> Dict[str, Any]:
    try:
        query = """
        MATCH (a:Agent {id: $agent_id})
        OPTIONAL MATCH (a)-[:USES_TOOL]->(t:Tool)
        OPTIONAL MATCH (a)-[:ACCESSES_MCP]->(m:MCP)
        RETURN a, collect(t) as tools, collect(m) as mcps
        """
        with kg.driver.session() as session:
            result = session.run(query, agent_id=agent_id)
            record = result.single()
            if record:
                return {
                    "agent": dict(record["a"]),
                    "tools": [dict(t) for t in record["tools"] if t],
                    "mcps": [dict(m) for m in record["mcps"] if m],
                }
            return {"agent": None, "tools": [], "mcps": []}
    except Exception as e:
        logger.error(f"Error querying Neo4j for agent details: {e}")
        return {"agent": None, "tools": [], "mcps": []}


def execute_tool(tool_id: str, params: Dict[str, Any]) -> str:
    try:
        if tool_id == "tool-007":  # Jira Task Creator
            return (
                f"Created Jira task with details: {params.get('task_details', 'N/A')}"
            )
        elif tool_id == "tool-008":  # CI/CD Pipeline Tool
            return f"Managed CI/CD pipeline with config: {params.get('pipeline_config', 'N/A')}"
        return "Tool execution not implemented"
    except Exception as e:
        logger.error(f"Error executing tool {tool_id}: {e}")
        return "Tool execution failed"


def fetch_mcp_data(mcp_id: str) -> str:
    try:
        # Simulate MCP data retrieval
        if mcp_id == "mcp-001":
            return "Retrieved sales data from PostgreSQL"
        return f"Data from {mcp_id} not available"
    except Exception as e:
        logger.error(f"Error fetching MCP data {mcp_id}: {e}")
        return "MCP data retrieval failed"


def generate_response(query: str, agent_data: Dict[str, Any]) -> str:
    try:
        generator = pipeline("text-generation", model="gpt2")
        context = (
            f"Query: {query}\n"
            f"Agent: {agent_data['agent']['name']} ({agent_data['agent']['description']})\n"
            f"Tools: {[tool['tool_name'] for tool in agent_data['tools']]}\n"
            f"MCPs: {[mcp['mcp_name'] for mcp in agent_data['mcps']]}"
        )
        response = generator(context, max_length=200, num_return_sequences=1)

        # Execute tools if available
        if agent_data["tools"]:
            tool_response = execute_tool(
                agent_data["tools"][0]["id"], {"task_details": query}
            )
            context += f"\nTool Output: {tool_response}"
            response = generator(context, max_length=250, num_return_sequences=1)

        # Fetch MCP data if needed
        if not response[0]["generated_text"].strip():
            mcp_response = (
                fetch_mcp_data(agent_data["mcps"][0]["id"])
                if agent_data["mcps"]
                else "No MCP data"
            )
            context += f"\nMCP Data: {mcp_response}"
            response = generator(context, max_length=300, num_return_sequences=1)

        return response[0]["generated_text"]
    except Exception as e:
        logger.error(f"Error generating response: {e}")
        return "Error generating response"


def process_query(kg: KnowledgeGraph, query: str) -> Dict[str, Any]:
    try:
        if not query or not isinstance(query, str):
            return {"error": "Invalid query input"}

        cached = redis_client.get(query)
        if cached:
            return json.loads(cached)

        relevant_agents = find_relevant_agents(query)
        if not relevant_agents:
            return {"error": "No relevant agents found"}

        top_agent_id = relevant_agents[0]["id"]
        details = get_agent_details(kg, top_agent_id)
        agent_caps = details["agent"].get("capabilities", [])

        if not has_required_knowledge(agent_caps, query):
            alternatives = (
                find_relevant_agents(query, top_k=2)[1:]
                if len(find_relevant_agents(query, top_k=2)) > 1
                else []
            )
            if alternatives:
                top_agent_id = alternatives[0]["id"]
                details = get_agent_details(kg, top_agent_id)
            else:
                return {"error": "No suitable agent found with required knowledge"}

        response = {
            "agent": details["agent"],
            "tools": details["tools"],
            "mcps": details["mcps"],
            "relevance_score": relevant_agents[0]["score"],
        }
        cache_response(query, response)
        return response
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        return {"error": f"Query processing failed: {e}"}


def cache_response(query: str, response: Dict[str, Any]):
    redis_client.setex(query, 3600, json.dumps(response))  # Cache for 1 hour


def has_required_knowledge(agent_caps: List[str], query: str) -> bool:
    query_lower = query.lower()
    return any(keyword in query_lower for keyword in agent_caps)


def main():
    kg = KnowledgeGraph(NEO4J_URI, NEO4J_USER, NEO4J_PASS)
    try:
        create_sample_knowledge_graph(kg)
        index_agents_to_pinecone(kg)

        # Example queries
        queries = [
            "hey",
        ]
        for query in queries:
            response_data = process_query(kg, query)
            if "error" not in response_data:
                response = generate_response(query, response_data)
                print(f"Query: {query}\nResponse: {response}\n")
            else:
                print(f"Query: {query}\nError: {response_data['error']}\n")
    except Exception as e:
        logger.error(f"Error in main: {e}")
    finally:
        kg.close()


if __name__ == "__main__":
    main()
