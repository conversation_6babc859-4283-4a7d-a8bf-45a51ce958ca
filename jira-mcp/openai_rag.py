import json
from pathlib import Path
import os
import requests
import typesense
from dotenv import load_dotenv

# --- 1. SETUP ---
# Load environment variables from .env file
load_dotenv()


# client = typesense.Client(
#     {
#         "nodes": [
#             {
#                 "host": "",  # For Typesense Cloud use xxx.a1.typesense.net
#                 "port": "443",  # For Typesense Cloud use 443
#                 "protocol": "https",  # For Typesense Cloud use https
#             }
#         ],
#         "api_key": "zPS6RC86QrGqnJSNF1xX5zC42mSNSyWZ",
#         "connection_timeout_seconds": 300,
#     }
# )


# Configure Typesense Client (this part remains the same)
try:
    client = typesense.Client({
        'nodes': [{
            'host': os.environ["TYPESENSE_HOST"],
            'port': os.environ["TYPESENSE_PORT"],
            'protocol': os.environ["TYPESENSE_PROTOCOL"]
        }],
        'api_key': os.environ["TYPESENSE_API_KEY"],
        'connection_timeout_seconds': 300
    })
except Key<PERSON>rror as e:
    raise Exception(f"Typesense environment variable {e} not set.")



def create_collection():
    collection_schema = {
        "name": "agents",
        "fields": [
            {"name": "id", "type": "string", "facet": False},
            {"name": "name", "type": "string", "facet": False},
            {"name": "description", "type": "string", "facet": False},
            {"name": "item_type", "type": "string", "facet": False},
            {"name": "category", "type": "string", "facet": False},
            {
                "name": "embedding",
                "type": "float[]",
                "embed": {
                    "from": ["description"],
                    "model_config": {"model_name": "ts/all-MiniLM-L12-v2"},
                },
            },
        ],
    }

    client.collections.create(collection_schema)


def import_data():
    AGENTS_FILE = Path("agents_registry.json")

    with open(AGENTS_FILE) as f:
        documents_json = json.load(f)

    documents = [
        {
            "id": agent["id"],
            "name": agent["name"],
            "description": agent.get("description", ""),
            "item_type": agent.get("item_type", ""),
            "category": str(agent.get("category", "")),
        }
        for agent in documents_json
    ]

    res = client.collections["agents"].documents.import_(
        documents, {"action": "create"}
    )

    print(f"response: {res}")


def create_model():
    url = "https://69w0gybxi2nuo38qp-1.a1.typesense.net/nl_search_models"

    headers = {
        "X-TYPESENSE-API-KEY": "Iu37Vz7yckuguDgr2IUf3rU2dNppjfGe",
        "Content-Type": "application/json",
    }
    payload = {
        "id": "discovery",
        "model_name": "openai/gpt-4.1",
        "api_url": "https://router.requesty.ai/v1",
        "api_key": "sk-o+Bg84ipT4+v8Vj+WKmdoSjQiBlBKJzmoUwtMj9IHIMqcyKPFT4HfDc4sX0+E25OyzQyUqmZycx32F71s7hDnE7EgKR+NGuBG9ArXuoIEwE=",
        "max_bytes": 16000,
    }

    response = requests.post(url, headers=headers, json=payload)

    # Check for success
    if response.ok:
        print("Model registered successfully:")
        print(response.json())
    else:
        print(f"Error {response.status_code}: {response.text}")


def search_agents(query):
    search_parameters = {
        "q": query,
        "query_by": "name,description,embedding",
        "rerank_hybrid_matches": True,
        "vector_query": "embedding:([], alpha: 0.8, distance_threshold:0.55)",
        "exclude_fields": "embedding",
    }

    results = client.collections["agents"].documents.search(search_parameters)
    print(f"Search results: {results}")
    return results


if __name__ == "__main__":
    # create_collection()
    # import_data()
    # create_model()
    search_agents("employee than can generate ppts")