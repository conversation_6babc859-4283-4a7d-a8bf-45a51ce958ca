import os

from dotenv import load_dotenv

load_dotenv()

groq_api_key = os.environ["GROQ_API_KEY"]

import json
import logging
from typing import Any, Dict, List, Optional

import requests
from neo4j import GraphDatabase
from sentence_transformers import SentenceTransformer
from typesense import Client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeGraph:

    def __init__(self, uri: str, username: str, password: str):
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        self.verify_connectivity()

    def close(self):
        self.driver.close()

    def verify_connectivity(self):
        try:
            with self.driver.session() as session:
                session.run("RETURN 1")
                logger.info("Successfully connected to Neo4j database")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise

    def get_agent_by_intent(self, intent: str) -> Optional[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (a:Agent)
                WHERE toLower(a.description) CONTAINS toLower($intent)
                RETURN a LIMIT 1
                """,
                intent=intent,
            )
            record = result.single()
            return dict(record["a"]) if record else None


class DirectTypeSenseRAG:

    def __init__(self, config: dict, collection_name: str):
        self.client = Client(config)
        self.collection_name = collection_name
        self.embedder = SentenceTransformer("all-MiniLM-L6-v2")

    def query(self, question: str, agent_id: Optional[str] = None, top_k: int = 5):
        query_vector = self.embedder.encode(question).tolist()

        search_params = {
            "q": "*",
            "vector_query": f"embedding:([{','.join(map(str, query_vector))}], k:{top_k})",
        }

        if agent_id:
            search_params["filter_by"] = f"agent_id:={agent_id}"

        results = self.client.collections[self.collection_name].documents.search(
            search_params
        )
        return [hit["document"]["content"] for hit in results["hits"]]


def send_to_llm(context: str, question: str) -> str:
    prompt = f"Context:\n{context}\n\nQuestion: {question}"
    headers = {"Authorization": f"Bearer {groq_api_key}"}  # Replace with actual API key
    response = requests.post(
        "https://api.groq.com/v1/chat/completions",
        headers=headers,
        json={
            "model": "mixtral-8x7b-32768",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0,
        },
    )
    return response.json()["choices"][0]["message"]["content"]


# === USAGE EXAMPLE ===

if __name__ == "__main__":

    # Neo4j setup
    NEO4J_URI = os.getenv("NEO4J_URI")
    NEO4J_USER = os.getenv("NEO4J_USER")
    NEO4J_PASS = os.getenv("NEO4J_PASS")

    kg = KnowledgeGraph(NEO4J_URI, NEO4J_USER, NEO4J_PASS)

    # RAG vector path
    VECTOR_PATH = "rag_vector_index"
    # rag = RAGPipeline(vector_dir=VECTOR_PATH)
    rag = DirectTypeSenseRAG(config=TYPESENSE_CONFIG, collection_name=COLLECTION_NAME)

    # User query
    user_query = "How can I generate charts from SQL results?"

    # Extract intent and find agent from KG
    agent = kg.get_agent_by_intent("chart sql")
    if agent:
        logger.info(f"Routing to agent: {agent['name']} ({agent['id']})")
        result = rag.query(user_query, agent_id=agent["id"])
    else:
        result = rag.query(user_query)

    print("\n--- Response ---\n")
    print(result)

    kg.close()
