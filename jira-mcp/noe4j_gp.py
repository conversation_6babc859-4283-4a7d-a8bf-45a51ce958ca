import logging
from typing import Any, Dict, List, Optional

from dotenv import load_dotenv
from neo4j import GraphDatabase

load_dotenv(override=True)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeGraph:

    def __init__(self, uri: str, username: str, password: str):
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        self.verify_connectivity()

    def close(self):
        self.driver.close()

    def verify_connectivity(self):
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 1")
                logger.info("Successfully connected to Neo4j database")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise

    def clear_database(self):
        with self.driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
            logger.info("Database cleared")

    def create_constraints(self):
        with self.driver.session() as session:
            constraints = [
                "CREATE CONSTRAINT agent_id IF NOT EXISTS FOR (a:Agent) REQUIRE a.id IS UNIQUE",
                "CREATE CONSTRAINT mcp_id IF NOT EXISTS FOR (m:MCP) REQUIRE m.id IS UNIQUE",
                "CREATE CONSTRAINT tool_id IF NOT EXISTS FOR (t:Tool) REQUIRE t.id IS UNIQUE",
            ]

            for constraint in constraints:
                try:
                    session.run(constraint)
                    logger.info(f"Created constraint: {constraint.split()[2]}")
                except Exception as e:
                    logger.debug(f"Constraint may already exist: {e}")

    def create_agent(
        self, id: str, name: str, description: str, **kwargs
    ) -> Dict[str, Any]:
        with self.driver.session() as session:
            properties = {"id": id, "name": name, "description": description}
            properties.update(kwargs)

            result = session.run(
                """
                CREATE (a:Agent $props)
                RETURN a
                """,
                props=properties,
            )
            record = result.single()
            logger.info(f"Created agent: {name} ({id})")
            return dict(record["a"])

    def get_agent(self, id: str) -> Optional[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (a:Agent {id: $id}) RETURN a", id=id)
            record = result.single()
            return dict(record["a"]) if record else None

    def get_all_agents(self) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (a:Agent) RETURN a ORDER BY a.name")
            return [dict(record["a"]) for record in result]

    def create_mcp(
        self, id: str, name: str, description: str, agent_id: str, **kwargs
    ) -> Dict[str, Any]:
        with self.driver.session() as session:
            # Start a transaction to ensure atomicity
            tx = session.begin_transaction()
            try:
                # First, verify the agent exists
                agent_check = tx.run(
                    "MATCH (a:Agent {id: $agent_id}) RETURN a", agent_id=agent_id
                )
                if not agent_check.single():
                    raise ValueError(f"Agent with id '{agent_id}' does not exist")

                # Create the MCP properties
                properties = {"id": id, "name": name, "description": description}
                properties.update(kwargs)

                # Create the MCP and relationship in a single transaction
                result = tx.run(
                    """
                    MATCH (a:Agent {id: $agent_id})
                    CREATE (m:MCP $props)
                    CREATE (a)-[:ACCESSES_MCP]->(m)
                    RETURN m, a
                    """,
                    agent_id=agent_id,
                    props=properties,
                )
                record = result.single()

                # Commit the transaction
                tx.commit()

                logger.info(
                    f"Created MCP: {name} ({id}) and linked to agent: {agent_id}"
                )
                return {
                    "mcp": dict(record["m"]),
                    "relationship_created": True,
                    "linked_agent": agent_id,
                }

            except Exception as e:
                tx.rollback()
                logger.error(f"Failed to create MCP {name} ({id}): {e}")
                raise

    def get_mcp(self, id: str) -> Optional[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (m:MCP {id: $id}) RETURN m", id=id)
            record = result.single()
            return dict(record["m"]) if record else None

    def get_all_mcps(self) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (m:MCP) RETURN m ORDER BY m.name")
            return [dict(record["m"]) for record in result]

    def create_tool(
        self, id: str, name: str, description: str, agent_id: str, **kwargs
    ) -> Dict[str, Any]:
        with self.driver.session() as session:
            # Start a transaction to ensure atomicity
            tx = session.begin_transaction()
            try:
                # First, verify the agent exists
                agent_check = tx.run(
                    "MATCH (a:Agent {id: $agent_id}) RETURN a", agent_id=agent_id
                )
                if not agent_check.single():
                    raise ValueError(f"Agent with id '{agent_id}' does not exist")

                # Create the tool properties
                properties = {"id": id, "name": name, "description": description}
                properties.update(kwargs)

                # Create the tool and relationship in a single transaction
                result = tx.run(
                    """
                    MATCH (a:Agent {id: $agent_id})
                    CREATE (t:Tool $props)
                    CREATE (a)-[:USES_TOOL]->(t)
                    RETURN t, a
                    """,
                    agent_id=agent_id,
                    props=properties,
                )
                record = result.single()

                # Commit the transaction
                tx.commit()

                logger.info(
                    f"Created tool: {name} ({id}) and linked to agent: {agent_id}"
                )
                return {
                    "tool": dict(record["t"]),
                    "relationship_created": True,
                    "linked_agent": agent_id,
                }

            except Exception as e:
                tx.rollback()
                logger.error(f"Failed to create tool {name} ({id}): {e}")
                raise

    def get_tool(self, id: str) -> Optional[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (t:Tool {id: $id}) RETURN t", id=id)
            record = result.single()
            return dict(record["t"]) if record else None

    def get_all_tools(self) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run("MATCH (t:Tool) RETURN t ORDER BY t.name")
            return [dict(record["t"]) for record in result]

    def create_uses_tool_relationship(self, agent_id: str, tool_id: str, **kwargs):
        with self.driver.session() as session:
            session.run(
                """
                MATCH (a:Agent {id: $agent_id})
                MATCH (t:Tool {id: $tool_id})
                CREATE (a)-[:USES_TOOL $props]->(t)
                """,
                agent_id=agent_id,
                tool_id=tool_id,
                props=kwargs,
            )
            logger.info(f"Created USES_TOOL relationship: {agent_id} -> {tool_id}")

    def create_accesses_mcp_relationship(self, agent_id: str, mcp_id: str, **kwargs):
        with self.driver.session() as session:
            session.run(
                """
                MATCH (a:Agent {id: $agent_id})
                MATCH (m:MCP {id: $mcp_id})
                CREATE (a)-[:ACCESSES_MCP $props]->(m)
                """,
                agent_id=agent_id,
                mcp_id=mcp_id,
                props=kwargs,
            )
            logger.info(f"Created ACCESSES_MCP relationship: {agent_id} -> {mcp_id}")

    def find_agents_by_tool(self, tool_id: str) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (a:Agent)-[r:USES_TOOL]->(t:Tool {id: $tool_id})
                RETURN a.id as agent_id, a.name as agent_name, a.description as agent_description,
                       r as relationship_props
                """,
                tool_id=tool_id,
            )
            return [dict(record) for record in result]

    def find_tools_by_agent(self, agent_id: str) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (a:Agent {id: $agent_id})-[r:USES_TOOL]->(t:Tool)
                RETURN t.id as tool_id, t.name as tool_name, t.description as tool_description,
                       r as relationship_props
                """,
                agent_id=agent_id,
            )
            return [dict(record) for record in result]

    def find_mcps_by_agent(self, agent_id: str) -> List[Dict[str, Any]]:
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (a:Agent {id: $agent_id})-[r:ACCESSES_MCP]->(m:MCP)
                RETURN m.id as mcp_id, m.name as mcp_name, m.description as mcp_description,
                       r as relationship_props
                """,
                agent_id=agent_id,
            )
            return [dict(record) for record in result]

    def get_entity_relationships(
        self, entity_type: str, entity_id: str
    ) -> Dict[str, Any]:
        with self.driver.session() as session:
            result = session.run(
                f"""
                MATCH (e:{entity_type} {{id: $entity_id}})
                OPTIONAL MATCH (e)-[r1]->(connected1)
                OPTIONAL MATCH (e)<-[r2]-(connected2)
                RETURN e, 
                       collect(DISTINCT {{type: type(r1), target: connected1, relationship: r1}}) as outgoing,
                       collect(DISTINCT {{type: type(r2), source: connected2, relationship: r2}}) as incoming
                """,
                entity_id=entity_id,
            )
            record = result.single()

            if not record:
                return {"error": f"{entity_type} not found"}

            return {
                "entity": dict(record["e"]),
                "outgoing_relationships": [
                    r for r in record["outgoing"] if r["type"] is not None
                ],
                "incoming_relationships": [
                    r for r in record["incoming"] if r["type"] is not None
                ],
            }


def create_sample_knowledge_graph():

    import os

    # Neo4j setup
    NEO4J_URI = os.getenv("NEO4J_URI")
    NEO4J_USER = os.getenv("NEO4J_USER")
    NEO4J_PASS = os.getenv("NEO4J_PASS")

    kg = KnowledgeGraph(NEO4J_URI, NEO4J_USER, NEO4J_PASS)

    try:
        kg.clear_database()
        kg.create_constraints()

        kg.create_agent(
            "agent-001",
            "Data Analyst Agent",
            "Analyzes data and generates insights",
            capabilities=["data_analysis", "visualization", "reporting"],
        )
        kg.create_agent(
            "agent-002",
            "Code Assistant",
            "Helps with programming tasks",
            capabilities=["code_generation", "debugging", "testing"],
        )
        kg.create_agent(
            "agent-003",
            "Research Assistant",
            "Conducts research and information gathering",
            capabilities=["web_search", "document_analysis", "summarization"],
        )

        # Create MCPs with their associated agents
        kg.create_mcp(
            "mcp-001",
            "Database MCP",
            "Provides access to SQL databases",
            "agent-001",  # Data Analyst Agent
            data_source="postgresql",
            protocol_version="1.0",
        )
        kg.create_mcp(
            "mcp-002",
            "File System MCP",
            "Provides access to file system operations",
            "agent-002",  # Code Assistant
            data_source="local_files",
            protocol_version="1.0",
        )
        kg.create_mcp(
            "mcp-003",
            "API Gateway MCP",
            "Provides access to external APIs",
            "agent-003",  # Research Assistant
            data_source="rest_apis",
            protocol_version="1.0",
        )

        # Create tools with their associated agents
        kg.create_tool(
            "tool-001",
            "SQL Query Tool",
            "Executes SQL queries against databases",
            "agent-001",  # Data Analyst Agent
            function_name="execute_sql",
            parameters=["query", "database"],
        )
        kg.create_tool(
            "tool-002",
            "File Reader Tool",
            "Reads files from the file system",
            "agent-002",  # Code Assistant
            function_name="read_file",
            parameters=["file_path"],
        )
        kg.create_tool(
            "tool-003",
            "Chart Generator",
            "Creates charts and visualizations",
            "agent-001",  # Data Analyst Agent
            function_name="create_chart",
            parameters=["data", "chart_type"],
        )
        kg.create_tool(
            "tool-004",
            "Web Search Tool",
            "Searches the web for information",
            "agent-003",  # Research Assistant
            function_name="web_search",
            parameters=["query", "max_results"],
        )

        # Note: Tool and MCP relationships are now created automatically
        # when tools and MCPs are created with their required agent_id parameter

        logger.info("Sample knowledge graph created successfully!")

        print("\n=== KNOWLEDGE GRAPH QUERIES ===")

        print("\n1. All Agents:")
        agents = kg.get_all_agents()
        for agent in agents:
            print(f"   {agent['name']} ({agent['id']}): {agent['description']}")

        print("\n2. Tools used by Data Analyst Agent:")
        tools = kg.find_tools_by_agent("agent-001")
        for tool in tools:
            print(f"   {tool['tool_name']}: {tool['tool_description']}")

        print("\n4. Data Analyst Agent relationships:")
        relationships = kg.get_entity_relationships("Agent", "agent-001")
        print(f"   Entity: {relationships['entity']['name']}")
        print(
            f"   Outgoing: {len(relationships['outgoing_relationships'])} relationships"
        )
        print(
            f"   Incoming: {len(relationships['incoming_relationships'])} relationships"
        )

    except Exception as e:
        logger.error(f"Error creating knowledge graph: {e}")
    finally:
        kg.close()


if __name__ == "__main__":
    create_sample_knowledge_graph()
