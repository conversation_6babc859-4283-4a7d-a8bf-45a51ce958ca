"""
Utility functions for retrieving and managing Atlassian Cloud IDs.
This module provides functions to programmatically fetch cloud IDs from the Atlassian API,
removing the dependency on environment variables.
"""

import logging
from typing import Any, Dict, List, Optional

import requests

logger = logging.getLogger(__name__)


def get_cloud_id_from_api(
    access_token: str, suppress_errors: bool = False
) -> Optional[str]:
    """
    Fetch the Atlassian Cloud ID using the Atlassian API.

    Args:
        access_token: The OAuth access token for authentication
        suppress_errors: If True, suppress error logging (useful when cloud ID is available from other sources)

    Returns:
        The cloud ID if found, None otherwise
    """
    if not suppress_errors:
        logger.info("Fetching cloud ID from Atlassian API")

    headers = {"Authorization": f"Bearer {access_token}", "Accept": "application/json"}

    try:
        response = requests.get(
            "https://api.atlassian.com/oauth/token/accessible-resources",
            headers=headers,
        )

        if response.status_code == 200:
            resources = response.json()
            if resources:
                cloud_id = resources[0]["id"]
                if not suppress_errors:
                    logger.info(f"Successfully fetched cloud ID from API: {cloud_id}")
                return cloud_id
            else:
                if not suppress_errors:
                    logger.error(
                        "API returned empty resources list. User may not have access to any Jira instances."
                    )
        else:
            if not suppress_errors:
                logger.error(
                    f"Failed to get cloud ID from API. Status code: {response.status_code}"
                )
                try:
                    error_content = response.json()
                    logger.error(f"Error response: {error_content}")
                except:
                    logger.error(f"Error response text: {response.text}")
    except Exception as e:
        if not suppress_errors:
            import traceback

            logger.error(f"Failed to get cloud ID: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")

    return None


def get_cloud_id(
    access_token: str, cached_cloud_id: Optional[str] = None
) -> Optional[str]:
    """
    Get the Atlassian Cloud ID, using a cached value if available or fetching programmatically from the API.

    Args:
        access_token: The OAuth access token for authentication
        cached_cloud_id: A previously cached cloud ID to use if available

    Returns:
        The cloud ID if found, None otherwise
    """
    # First check if we have a cached cloud ID
    if cached_cloud_id:
        logger.debug(f"Using cached cloud ID: {cached_cloud_id}")
        return cached_cloud_id

    # Fetch programmatically from the API
    return get_cloud_id_from_api(access_token)


def get_api_url(cloud_id: str, path: str) -> str:
    """
    Construct a Jira API URL using the cloud ID.

    Args:
        cloud_id: The Atlassian Cloud ID
        path: The API path (e.g., "/rest/api/3/issue")

    Returns:
        The full API URL
    """
    # Remove leading slash if present
    if path.startswith("/"):
        path = path[1:]

    api_url = f"https://api.atlassian.com/ex/jira/{cloud_id}/{path}"
    logger.debug(f"Constructed API URL: {api_url}")

    return api_url
