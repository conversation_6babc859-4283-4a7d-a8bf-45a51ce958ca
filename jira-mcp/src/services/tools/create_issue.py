"""Module for creating a new Jira issue with user assignment."""

import json
import re
from functools import lru_cache
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseTool


class CreateIssueTool(BaseTool):
    """
    Create a new Jira issue using the v3 API with summary, description, type, project, priority,
    story points, sprint assignment, and assignee.

    Example:
        {
            "summary": "API endpoint is slow",
            "description": "The /api/v1/data endpoint takes over 5 seconds to respond.",
            "issueType": "Bug",
            "projectKey": "API",
            "priority": "High",
            "assignee": "<EMAIL>",
            "storyPoints": 8,
            "sprintId": 123
        }
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="create_issue",
            description="Create a new Jira issue.",
            inputSchema={
                "type": "object",
                "properties": {
                    "summary": {
                        "type": "string",
                        "description": "A concise title for the issue.",
                    },
                    "description": {
                        "type": "string",
                        "description": "A detailed plain text description of the issue.",
                    },
                    "issueType": {
                        "type": "string",
                        "description": "Type of issue (e.g., 'Task').",
                    },
                    "projectKey": {
                        "type": "string",
                        "description": "Jira project key (e.g., 'TEST').",
                    },
                    "priority": {
                        "type": "string",
                        "description": "Priority of the issue (e.g., 'High', 'Medium', 'Low'). Case-insensitive.",
                    },
                    "assignee": {
                        "type": "string",
                        "description": "Email, username, or account ID of the user to assign the issue to (optional).",
                    },
                    "storyPoints": {
                        "type": "number",
                        "description": "Optional story point estimate for the issue (e.g., 3, 5, 8).",
                    },
                    "sprintId": {
                        "type": "number",
                        "description": "Optional Sprint ID to add the issue to an active sprint.",
                    },
                },
                "required": ["summary", "issueType", "projectKey"],
            },
        )

    # --- HELPER METHODS ---

    def _is_valid_account_id(self, identifier: str) -> bool:
        """
        Check if the identifier is a valid account ID for Jira Cloud.

        Args:
            identifier (str): User identifier to validate.

        Returns:
            bool: True if the identifier is a valid account ID, False otherwise.
        """
        # Jira Cloud account IDs are typically 24-char hex or <number>:<hash> format
        return bool(re.match(r"^[0-9a-f]{24}$|^[0-9]+:[0-9a-f-]+$", identifier))

    async def _lookup_user_directly(
        self, assignee: str, jira_fetcher, cloudid: str
    ) -> Optional[str]:
        """
        Look up a user account ID directly using Jira's user search API.

        Args:
            assignee (str): Username, email, or display name to look up.
            jira_fetcher: The fetcher object for making API requests.
            cloudid (str): Cloud ID for Jira Cloud API calls.

        Returns:
            Optional[str]: Account ID if found, None otherwise.
        """
        try:
            url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/user/search"
            params = {"query": assignee, "maxResults": 1}
            headers = {"Accept": "application/json"}

            response = jira_fetcher.get(url, headers=headers, params=params)
            if not response.ok:
                print(
                    f"DEBUG: Direct lookup failed for '{assignee}': {response.status_code}"
                )
                return None

            users = response.json()
            if not isinstance(users, list):
                print(
                    f"DEBUG: Unexpected response type from user search: {type(users)}"
                )
                return None

            for user in users:
                if (
                    user.get("displayName", "").lower() == assignee.lower()
                    or user.get("name", "").lower() == assignee.lower()
                    or user.get("emailAddress", "").lower() == assignee.lower()
                ):
                    if "accountId" in user:
                        return user["accountId"]
            return None
        except Exception as e:
            print(f"DEBUG: Error in direct user lookup for '{assignee}': {str(e)}")
            return None

    async def _lookup_user_by_permissions(
        self, assignee: str, jira_fetcher, cloudid: str
    ) -> Optional[str]:
        """
        Look up a user account ID by permissions (Cloud only).

        Args:
            assignee (str): Username, email, or display name to look up.
            jira_fetcher: The fetcher object for making API requests.
            cloudid (str): Cloud ID for Jira Cloud API calls.

        Returns:
            Optional[str]: Account ID if found, None otherwise.
        """
        try:
            url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/2/user/permission/search"
            params = {"query": assignee, "permissions": "BROWSE"}
            headers = {"Accept": "application/json"}

            response = jira_fetcher.get(url, headers=headers, params=params)
            if not response.ok:
                print(
                    f"DEBUG: Permissions lookup failed for '{assignee}': {response.status_code}"
                )
                return None

            data = response.json()
            for user in data.get("users", []):
                if "accountId" in user:
                    return user["accountId"]
            return None
        except Exception as e:
            print(f"DEBUG: Error in permissions lookup for '{assignee}': {str(e)}")
            return None

    async def _find_user_account_id(
        self, assignee: str, jira_fetcher, cloudid: str
    ) -> Optional[str]:
        """
        Find the account ID for a username, email, or account ID.

        Args:
            assignee (str): Username, email, or account ID.
            jira_fetcher: The fetcher object for making API requests.
            cloudid (str): Cloud ID for Jira Cloud API calls.

        Returns:
            Optional[str]: Account ID if found, None otherwise.
        """
        if self._is_valid_account_id(assignee):
            print(f"DEBUG: Assignee '{assignee}' is already a valid account ID.")
            return assignee

        account_id = await self._lookup_user_directly(assignee, jira_fetcher, cloudid)
        if account_id:
            print(
                f"DEBUG: Found account ID '{account_id}' via direct lookup for '{assignee}'."
            )
            return account_id

        account_id = await self._lookup_user_by_permissions(
            assignee, jira_fetcher, cloudid
        )
        if account_id:
            print(
                f"DEBUG: Found account ID '{account_id}' via permissions lookup for '{assignee}'."
            )
            return account_id

        print(f"DEBUG: Could not find account ID for user: {assignee}")
        return None

    @lru_cache(maxsize=1)
    def _get_all_priorities(self, jira_fetcher, cloudid: str) -> List[Dict]:
        """
        Fetch all available priorities from Jira.

        Args:
            jira_fetcher: The fetcher object for making API requests.
            cloudid (str): Cloud ID for Jira Cloud API calls.

        Returns:
            List[Dict]: List of priority objects.
        """
        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/priority"
        response = jira_fetcher.get(url, headers={"Accept": "application/json"})
        return response.json() if response.ok else []

    def _get_priority_id(
        self, priority_name: str, jira_fetcher, cloudid: str
    ) -> Optional[str]:
        """
        Get the priority ID for a given priority name.

        Args:
            priority_name (str): Name of the priority (e.g., 'High').
            jira_fetcher: The fetcher object for making API requests.
            cloudid (str): Cloud ID for Jira Cloud API calls.

        Returns:
            Optional[str]: Priority ID if found, None otherwise.
        """
        if not priority_name:
            return None
        for p in self._get_all_priorities(jira_fetcher, cloudid):
            if p.get("name", "").lower() == priority_name.lower():
                return p.get("id")
        return None

    def _create_adf_from_string(self, text_content: str) -> Dict:
        """
        Creates a valid Atlassian Document Format (ADF) JSON object from plain text.

        Args:
            text_content (str): Plain text content to convert.

        Returns:
            Dict: ADF JSON object, or None if text_content is empty.
        """
        if not text_content:
            return None
        paragraphs = [
            {"type": "paragraph", "content": [{"type": "text", "text": t}]}
            for t in text_content.strip().split("\n")
            if t
        ]
        return (
            {"type": "doc", "version": 1, "content": paragraphs} if paragraphs else None
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        """
        Create a new Jira issue with the specified fields.

        Args:
            arguments (Dict): Input arguments containing issue details.
            jira_fetcher: The fetcher object for making API requests.
            cloudid (Optional[str]): Cloud ID for Jira Cloud API calls.

        Returns:
            List[TextContent]: Response indicating success or failure with warnings.

        Raises:
            RuntimeError: If cloudid is missing or API call fails.
            ValueError: If required arguments are missing.
        """
        if not cloudid:
            raise RuntimeError("Missing cloudid for Jira Cloud API call.")

        required_fields = ["summary", "issueType", "projectKey"]
        if not all(arguments.get(field) for field in required_fields):
            raise ValueError("summary, issueType, and projectKey are required")

        warnings = {}
        payload = {
            "fields": {
                "project": {"key": arguments["projectKey"]},
                "summary": arguments["summary"],
                "issuetype": {"name": arguments["issueType"]},
            }
        }

        if "description" in arguments and arguments["description"]:
            adf_description = self._create_adf_from_string(arguments["description"])
            if adf_description:
                payload["fields"]["description"] = adf_description
            else:
                warnings["description"] = (
                    "Description is empty or invalid. Skipping description."
                )

        if arguments.get("priority"):
            pid = self._get_priority_id(arguments["priority"], jira_fetcher, cloudid)
            if pid:
                payload["fields"]["priority"] = {"id": pid}
            else:
                warnings["priority"] = (
                    f"Priority '{arguments['priority']}' not found. Using default priority."
                )

        if arguments.get("assignee"):
            aid = await self._find_user_account_id(
                arguments["assignee"], jira_fetcher, cloudid
            )
            if aid:
                payload["fields"]["assignee"] = {"accountId": aid}
                print(
                    f"DEBUG: Assigned issue to account ID '{aid}' for '{arguments['assignee']}'."
                )
            else:
                warnings["assignee"] = (
                    f"Assignee '{arguments['assignee']}' not found. Skipping assignment."
                )

        if "storyPoints" in arguments:
            try:
                if arguments["storyPoints"] > 50:
                    warnings["storyPoints"] = (
                        f"Story point value '{arguments['storyPoints']}' is unusually high."
                    )
                payload["fields"]["customfield_10016"] = arguments["storyPoints"]
            except Exception:
                warnings["storyPoints"] = (
                    "Story Points field could not be added. Possibly not configured for this project."
                )

        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue"
        response = jira_fetcher.post(url, json=payload)

        if not response.ok:
            error_details = response.text
            try:
                error_json = response.json()
                errors = error_json.get("errorMessages", []) + list(
                    error_json.get("errors", {}).values()
                )
                error_details = " | ".join(map(str, errors))
            except json.JSONDecodeError:
                pass
            raise RuntimeError(
                f"Failed to create issue: {response.status_code} - {error_details}"
            )

        created = response.json()
        new_key = created.get("key")
        browse_url = (
            f"https://id.atlassian.com/browse/{new_key}"
            if new_key
            else "URL not available"
        )

        if arguments.get("sprintId") and new_key:
            sprint_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/agile/1.0/sprint/{arguments['sprintId']}/issue"
            sprint_response = jira_fetcher.post(sprint_url, json={"issues": [new_key]})
            if not sprint_response.ok:
                warnings["sprint"] = (
                    f"Sprint ID '{arguments['sprintId']}' is invalid or issue could not be added."
                )

        result = {
            "key": new_key,
            "id": created.get("id"),
            "url": browse_url,
            "message": f"Successfully created issue {new_key}.",
        }
        if warnings:
            result["warnings"] = warnings

        return [TextContent(type="text", text=json.dumps(result, indent=2))]
