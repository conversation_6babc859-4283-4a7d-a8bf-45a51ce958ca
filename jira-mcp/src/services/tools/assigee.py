"""Module for assigning a user to a Jira issue."""

import json
import re
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseTool


class AssignIssueTool(BaseTool):
    """
    Assign a user to a Jira issue using their username, email, or account ID.
    Example:
    {
        "issueKey": "TEST-123",
        "assignee": "<EMAIL>"
    }

    Field details:
        - issueKey: (str) Key of the issue to assign (e.g., 'TEST-123').
        - assignee: (str) Username, email, or account ID of the user to assign.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="assign_issue",
            description="Assign a user to a Jira issue by their username, email, or account ID.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "Key of the issue to assign (e.g., 'TEST-123').",
                    },
                    "assignee": {
                        "type": "string",
                        "description": "Username, email, or account ID of the user to assign.",
                    },
                },
                "required": ["issueKey", "assignee"],
            },
        )

    def _is_valid_account_id(self, identifier: str, is_cloud: bool) -> bool:
        """
        Check if the identifier is a valid account ID for Cloud or key/name for Server/DC.

        Args:
            identifier (str): User identifier to validate.
            is_cloud (bool): Whether the Jira instance is Cloud.

        Returns:
            bool: True if the identifier is a valid account ID, False otherwise.
        """
        if is_cloud:
            # Jira Cloud account IDs are typically 24-char hex or <number>:<hash> format
            return bool(re.match(r"^[0-9a-f]{24}$|^[0-9]+:[0-9a-f-]+$", identifier))
        # Jira Server/DC uses username or key, typically non-empty strings
        return bool(identifier and len(identifier) >= 1)

    async def _lookup_user_directly(
        self, assignee: str, jira_fetcher, cloudid: Optional[str], is_cloud: bool
    ) -> Optional[str]:
        """
        Look up a user account ID directly using Jira's user search API.

        Args:
            assignee (str): Username, email, or display name to look up.
            jira_fetcher: The fetcher object for making API requests.
            cloudid (Optional[str]): Cloud ID for Jira Cloud API calls.
            is_cloud (bool): Whether the Jira instance is Cloud.

        Returns:
            Optional[str]: Account ID if found, None otherwise.
        """
        try:
            base_url = (
                f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3"
                if is_cloud and cloudid
                else "/rest/api/2"
            )
            url = f"{base_url}/user/search"
            params = {"query" if is_cloud else "username": assignee, "maxResults": 1}
            headers = {"Accept": "application/json"}

            response = jira_fetcher.get(url, headers=headers, params=params)
            if not response.ok:
                return None

            users = response.json()
            if not isinstance(users, list):
                print(
                    f"DEBUG: Unexpected response type from user search: {type(users)}"
                )
                return None

            for user in users:
                if (
                    user.get("displayName", "").lower() == assignee.lower()
                    or user.get("name", "").lower() == assignee.lower()
                    or user.get("emailAddress", "").lower() == assigneeLOWER()
                ):
                    if is_cloud and "accountId" in user:
                        return user["accountId"]
                    elif not is_cloud:
                        return user.get("name") or user.get("key")
            return None
        except Exception as e:
            print(f"DEBUG: Error in direct user lookup for '{assignee}': {str(e)}")
            return None

    async def _lookup_user_by_permissions(
        self, assignee: str, jira_fetcher, cloudid: str
    ) -> Optional[str]:
        """
        Look up a user account ID by permissions (Cloud only).

        Args:
            assignee ( str): Username, email, or display name to look up.
            jira_fetcher: The fetcher object for making API requests.
            cloudid (str): Cloud ID for Jira Cloud API calls.

        Returns:
            Optional[str]: Account ID if found, None otherwise.
        """
        try:
            url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/2/user/permission/search"
            params = {"query": assignee, "permissions": "BROWSE"}
            headers = {"Accept": "application/json"}

            response = jira_fetcher.get(url, headers=headers, params=params)
            if not response.ok:
                return None

            data = response.json()
            for user in data.get("users", []):
                if "accountId" in user:
                    return user["accountId"]
            return None
        except Exception as e:
            print(f"DEBUG: Error in permissions lookup for '{assignee}': {str(e)}")
            return None

    async def _get_account_id(
        self, assignee: str, jira_fetcher, cloudid: Optional[str], is_cloud: bool
    ) -> str:
        """
        Get the account ID for a username, email, or account ID.

        Args:
            assignee (str): Username, email, or account ID.
            jira_fetcher: The fetcher object for making API requests.
            cloudid (Optional[str]): Cloud ID for Jira Cloud API calls.
            is_cloud (bool): Whether the Jira instance is Cloud.

        Returns:
            str: Account ID suitable for assigning to a Jira issue.

        Raises:
            ValueError: If the account ID could not be found.
        """
        if self._is_valid_account_id(assignee, is_cloud):
            print(f"DEBUG: Assignee '{assignee}' is already a valid account ID.")
            return assignee

        account_id = await self._lookup_user_directly(
            assignee, jira_fetcher, cloudid, is_cloud
        )
        if account_id:
            print(
                f"DEBUG: Found account ID '{account_id}' via direct lookup for '{assignee}'."
            )
            return account_id

        if is_cloud and cloudid:
            account_id = await self._lookup_user_by_permissions(
                assignee, jira_fetcher, cloudid
            )
            if account_id:
                print(
                    f"DEBUG: Found account ID '{account_id}' via permissions lookup for '{assignee}'."
                )
                return account_id

        raise ValueError(f"Could not find account ID for user: {assignee}")

    async def execute(
        self, arguments: dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        """
        Assign a user to a Jira issue.

        Args:
            arguments (dict): Input arguments containing issueKey and assignee.
            jira_fetcher: The fetcher object for making API requests.
            cloudid (Optional[str]): Cloud ID for Jira Cloud API calls.

        Returns:
            List[TextContent]: Response indicating success or failure.

        Raises:
            RuntimeError: If cloudid is missing for Jira Cloud or API call fails.
            ValueError: If required arguments are missing or invalid.
        """
        if not cloudid:
            raise RuntimeError("Missing cloudid for Jira Cloud API call.")

        issue_key = arguments.get("issueKey")
        assignee = arguments.get("assignee")

        if not issue_key or not assignee:
            raise ValueError("issueKey and assignee are required")

        # Assume is_cloud=True for this tool, as it uses cloudid
        account_id = await self._get_account_id(
            assignee, jira_fetcher, cloudid, is_cloud=True
        )

        url = (
            f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}"
        )
        headers = {"Accept": "application/json", "Content-Type": "application/json"}
        payload = {"fields": {"assignee": {"accountId": account_id}}}

        print(
            f"DEBUG: Assigning issue {issue_key} to account ID '{account_id}' with payload: {json.dumps(payload)}"
        )
        response = jira_fetcher.put(url, headers=headers, json=payload)

        if not response.ok:
            error_details = response.text
            try:
                error_json = response.json()
                error_details = error_json.get("errorMessages", []) + list(
                    error_json.get("errors", {}).values()
                )
                error_details = " | ".join(map(str, error_details))
            except json.JSONDecodeError:
                pass
            raise RuntimeError(
                f"Failed to assign issue: {response.status_code} - {error_details}"
            )

        browse_url = f"https://id.atlassian.com/browse/{issue_key}"
        return [
            TextContent(
                type="text",
                text=json.dumps(
                    {
                        "message": f"Issue {issue_key} assigned successfully to user with account ID '{account_id}'",
                        "url": browse_url,
                    }
                ),
            )
        ]
