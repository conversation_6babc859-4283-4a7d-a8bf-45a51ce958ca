import os
import json
import typesense
import google.generativeai as genai # CHANGED: Import Google's library
from dotenv import load_dotenv

# --- 1. SETUP ---
# Load environment variables from .env file
load_dotenv()

# CHANGED: Configure Google's Generative AI client
try:
    genai.configure(api_key=os.environ["GOOGLE_API_KEY"])
except KeyError:
    raise Exception("GOOGLE_API_KEY environment variable not set.")

# Configure Typesense Client (this part remains the same)
try:
    typesense_client = typesense.Client({
        'nodes': [{
            'host': os.environ["TYPESENSE_HOST"],
            'port': os.environ["TYPESENSE_PORT"],
            'protocol': os.environ["TYPESENSE_PROTOCOL"]
        }],
        'api_key': os.environ["TYPESENSE_API_KEY"],
        'connection_timeout_seconds': 300
    })
except KeyError as e:
    raise Exception(f"Typesense environment variable {e} not set.")

# --- Constants ---
COLLECTION_NAME = "tech_companies_gemini_rag"
# CHANGED: Google's text-embedding-004 model produces 768-dimensional vectors
EMBEDDING_DIMENSION = 768
# CHANGED: Define the models we'll use
EMBEDDING_MODEL = "models/text-embedding-004"
GENERATION_MODEL = "gemini-1.5-flash"

# --- 2. DATA PREPARATION ---
# Data remains the same as the previous example
documents = [
    {
        "id": "1",
        "company_name": "Xerox",
        "foundation_year": 1906,
        "description": "Xerox Holdings Corporation is an American corporation that sells print and digital document products and services. Its research division, PARC, is renowned for inventing the graphical user interface (GUI), the computer mouse, and desktop computing."
    },
    {
        "id": "2",
        "company_name": "Bell Labs",
        "foundation_year": 1925,
        "description": "Bell Labs, officially Nokia Bell Labs, was the research and development wing of the American Bell System. It is famous for numerous groundbreaking inventions, including the transistor, the laser, the C programming language, and the UNIX operating system."
    },
    {
        "id": "3",
        "company_name": "Fairchild Semiconductor",
        "foundation_year": 1957,
        "description": "Fairchild Semiconductor was a pioneering American semiconductor company that was instrumental in the development of the integrated circuit. Many of its employees, known as the 'traitorous eight', later founded other major tech companies, including Intel."
    },
    {
        "id": "4",
        "company_name": "IBM",
        "foundation_year": 1911,
        "description": "International Business Machines Corporation (IBM) is a multinational technology company known for producing computer hardware, middleware, and software. IBM was a dominant force in the mainframe computer era and has made significant contributions to areas like the relational database and the floppy disk."
    }
]

# --- 3. EMBEDDING & INDEXING ---
def setup_typesense_collection_with_gemini():
    """
    Creates a Typesense collection, generates embeddings using Gemini,
    and indexes the documents.
    """
    # Delete the collection if it already exists
    try:
        typesense_client.collections[COLLECTION_NAME].delete()
        print(f"Collection '{COLLECTION_NAME}' deleted.")
    except typesense.exceptions.ObjectNotFound:
        pass

    # Define the collection schema with the correct embedding dimension
    collection_schema = {
        "name": COLLECTION_NAME,
        "fields": [
            {"name": "company_name", "type": "string"},
            {"name": "foundation_year", "type": "int32", "facet": True},
            {"name": "description", "type": "string"},
            # Vector field for embeddings
            {"name": "embedding", "type": "float[]", "num_dim": EMBEDDING_DIMENSION}
        ]
    }

    print(f"Creating collection '{COLLECTION_NAME}'...")
    typesense_client.collections.create(collection_schema)

    print("Generating embeddings with Gemini and indexing documents...")
    docs_to_index = []
    for doc in documents:
        # CHANGED: Generate embedding using Google's model
        # The result is accessed directly from the 'embedding' key
        embedding = genai.embed_content(
            model=EMBEDDING_MODEL,
            content=doc["description"]
        )['embedding']

        doc_with_embedding = doc.copy()
        doc_with_embedding["embedding"] = embedding
        docs_to_index.append(doc_with_embedding)

    # Index all documents in a single batch
    import_results = typesense_client.collections[COLLECTION_NAME].documents.import_(
        docs_to_index, {'action': 'create'}
    )
    
    failed_imports = [result for result in import_results if not result['success']]
    if failed_imports:
        print("The following documents failed to import:")
        for failed in failed_imports:
            print(json.dumps(failed, indent=2))
    else:
        print("All documents indexed successfully!")

# --- 4. RAG PIPELINE (RETRIEVAL & GENERATION) ---
def ask_gemini(query: str, max_context_docs: int = 3, verbose: bool = False):
    """
    The main RAG pipeline function using Gemini.
    """
    print(f"\n🤔 Query: {query}")

    # --- 4a. Retrieval (using Gemini for query embedding) ---
    query_embedding = genai.embed_content(
        model=EMBEDDING_MODEL,
        content=query
    )['embedding']

    # Perform vector search in Typesense (same as before)
    search_params = {
        "q": "*",
        "vector_query": f"embedding:([{','.join(map(str, query_embedding))}], k:{max_context_docs})",
        "per_page": max_context_docs,
        "include_fields": "company_name,foundation_year,description"
    }
    search_results = typesense_client.collections[COLLECTION_NAME].documents.search(search_params)
    retrieved_docs = [hit['document'] for hit in search_results['hits']]

    if not retrieved_docs:
        print("No relevant documents found in Typesense.")
        return

    context_str = "\n---\n".join([
        f"Company: {doc['company_name']} (Founded: {doc['foundation_year']})\nDescription: {doc['description']}"
        for doc in retrieved_docs
    ])

    if verbose:
        print("\n📚 Retrieved Context:")
        print(context_str)

    # --- 4b. Generation (using Gemini for final answer) ---
    prompt = f"""
    Based ONLY on the following context, please answer the question.
    If the context does not contain the information needed to answer the question,
    state that you don't have enough information.

    Context:
    ---
    {context_str}
    ---

    Question: {query}
    """

    # CHANGED: Generate the answer using Gemini's model
    generation_model = genai.GenerativeModel(GENERATION_MODEL)
    response = generation_model.generate_content(prompt)

    answer = response.text
    print("\n✅ Generated Answer:")
    print(answer)
    return answer

# --- MAIN EXECUTION ---
if __name__ == "__main__":
    print("--- Setting up Typesense Collection with Gemini Embeddings ---")
    setup_typesense_collection_with_gemini()
    print("\n--- Setup Complete. Starting RAG queries with Gemini. ---")

    ask_gemini("Which company invented the UNIX operating system and the C language?", verbose=True)
    ask_gemini("Who were the 'traitorous eight' and what did they do?")
    ask_gemini("What is the stock price of IBM today?")