-- Migration: Add cloud_id column to oauth_credentials table
-- This migration adds support for multi-site Atlassian OAuth by adding a cloud_id column

-- Add cloud_id column
ALTER TABLE oauth_credentials 
ADD COLUMN cloud_id VARCHAR(255) NULL;

-- Create indexes for cloud_id
CREATE INDEX idx_oauth_cloud_id ON oauth_credentials(cloud_id);
CREATE INDEX idx_oauth_user_cloud ON oauth_credentials(user_id, cloud_id);

-- Update existing Jira credentials to use the new composite key format
-- This is a data migration that needs to be run carefully in production

-- First, let's identify existing Jira credentials that need updating
-- Note: This assumes existing Jira credentials are single-site and can be migrated
-- In a real production environment, you might need to handle this differently

-- For existing Jira credentials, we'll need to:
-- 1. Extract cloud_id from stored tokens (if available)
-- 2. Update composite_key to include provider and cloud_id
-- 3. Set cloud_id column

-- This is a placeholder for the data migration logic
-- In practice, you would need to:
-- 1. Query existing Jira OAuth credentials
-- 2. Retrieve their tokens from Secret Manager
-- 3. Extract cloud_id from tokens
-- 4. Update the composite_key and cloud_id fields

-- Example of what the update might look like (pseudo-code):
-- UPDATE oauth_credentials 
-- SET 
--   cloud_id = (extracted_from_tokens),
--   composite_key = CONCAT(user_id, '_', tool_name, '_', provider, '_', cloud_id)
-- WHERE provider = 'jira' AND cloud_id IS NULL;

-- For now, we'll just add a comment indicating manual migration is needed
-- MANUAL MIGRATION REQUIRED:
-- 1. Run a script to extract cloud_id from existing Jira tokens
-- 2. Update composite_key format for existing Jira credentials
-- 3. Verify all existing credentials still work after migration
