"""
Utility functions for Atlassian OAuth and site management.
"""

import logging
from typing import Dict, List, Optional, Any
import httpx

logger = logging.getLogger(__name__)


async def get_accessible_atlassian_sites(access_token: str) -> List[Dict[str, Any]]:
    """
    Fetch accessible Atlassian sites using the OAuth access token.
    
    Args:
        access_token: OAuth access token
        
    Returns:
        List of accessible sites with their details
        
    Raises:
        Exception: If API call fails
    """
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Accept": "application/json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                "https://api.atlassian.com/oauth/token/accessible-resources",
                headers=headers
            )
            
            if response.status_code == 200:
                resources = response.json()
                
                # Transform the response to include more user-friendly information
                sites = []
                for resource in resources:
                    site = {
                        "id": resource.get("id"),
                        "name": resource.get("name", "Unknown Site"),
                        "url": resource.get("url", ""),
                        "avatar_url": resource.get("avatarUrl", ""),
                        "scopes": resource.get("scopes", [])
                    }
                    sites.append(site)
                
                logger.info(f"Successfully fetched {len(sites)} accessible Atlassian sites")
                return sites
            else:
                logger.error(f"Failed to fetch accessible sites. Status: {response.status_code}")
                logger.error(f"Response: {response.text}")
                raise Exception(f"Failed to fetch accessible sites: {response.status_code}")
                
    except httpx.TimeoutException:
        logger.error("Timeout while fetching accessible Atlassian sites")
        raise Exception("Timeout while fetching accessible sites")
    except Exception as e:
        logger.error(f"Error fetching accessible Atlassian sites: {e}")
        raise


def requires_site_selection(sites: List[Dict[str, Any]]) -> bool:
    """
    Determine if site selection is required based on the number of accessible sites.
    
    Args:
        sites: List of accessible sites
        
    Returns:
        True if site selection is required, False otherwise
    """
    return len(sites) > 1


def get_default_site(sites: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """
    Get the default site when only one site is available.
    
    Args:
        sites: List of accessible sites
        
    Returns:
        The first (and presumably only) site, or None if no sites
    """
    return sites[0] if sites else None


def validate_site_selection(sites: List[Dict[str, Any]], selected_cloud_id: str) -> bool:
    """
    Validate that the selected cloud ID is among the accessible sites.
    
    Args:
        sites: List of accessible sites
        selected_cloud_id: The cloud ID selected by the user
        
    Returns:
        True if the selection is valid, False otherwise
    """
    return any(site["id"] == selected_cloud_id for site in sites)


def find_site_by_cloud_id(sites: List[Dict[str, Any]], cloud_id: str) -> Optional[Dict[str, Any]]:
    """
    Find a site by its cloud ID.
    
    Args:
        sites: List of accessible sites
        cloud_id: The cloud ID to search for
        
    Returns:
        The site dictionary if found, None otherwise
    """
    return next((site for site in sites if site["id"] == cloud_id), None)
